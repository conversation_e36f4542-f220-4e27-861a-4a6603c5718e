[{"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\vibe\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AiAssistant.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettings.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AutoSaveIndicator.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\LanguageToggle.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ModuleLayout.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\OutputPanel.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ProgressIndicator.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartQuestion.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeToggle.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\store\\contextStore.ts": "23", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\generate\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\models\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\validate\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page_backup.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartFieldAssistant.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\TestAIGeneration.tsx": "30", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\lib\\llmProviders.ts": "31"}, {"size": 1145, "mtime": 1751756292092, "results": "32", "hashOfConfig": "33"}, {"size": 1281, "mtime": 1751756314810, "results": "34", "hashOfConfig": "33"}, {"size": 4711, "mtime": 1751752039368, "results": "35", "hashOfConfig": "33"}, {"size": 5406, "mtime": 1751752064185, "results": "36", "hashOfConfig": "33"}, {"size": 11119, "mtime": 1751751211373, "results": "37", "hashOfConfig": "33"}, {"size": 793, "mtime": 1751769279175, "results": "38", "hashOfConfig": "33"}, {"size": 7233, "mtime": 1751752111799, "results": "39", "hashOfConfig": "33"}, {"size": 10485, "mtime": 1751756134293, "results": "40", "hashOfConfig": "33"}, {"size": 4641, "mtime": 1751752012669, "results": "41", "hashOfConfig": "33"}, {"size": 6963, "mtime": 1751752090185, "results": "42", "hashOfConfig": "33"}, {"size": 2582, "mtime": 1751749235443, "results": "43", "hashOfConfig": "33"}, {"size": 6525, "mtime": 1751756272373, "results": "44", "hashOfConfig": "33"}, {"size": 21199, "mtime": 1751761812515, "results": "45", "hashOfConfig": "33"}, {"size": 1584, "mtime": 1751752141042, "results": "46", "hashOfConfig": "33"}, {"size": 3733, "mtime": 1751762560955, "results": "47", "hashOfConfig": "33"}, {"size": 1882, "mtime": 1751755786921, "results": "48", "hashOfConfig": "33"}, {"size": 4031, "mtime": 1751752183700, "results": "49", "hashOfConfig": "33"}, {"size": 8630, "mtime": 1751769132291, "results": "50", "hashOfConfig": "33"}, {"size": 5420, "mtime": 1751751875523, "results": "51", "hashOfConfig": "33"}, {"size": 6100, "mtime": 1751772952346, "results": "52", "hashOfConfig": "33"}, {"size": 1709, "mtime": 1751749793754, "results": "53", "hashOfConfig": "33"}, {"size": 2382, "mtime": 1751755718062, "results": "54", "hashOfConfig": "33"}, {"size": 10350, "mtime": 1751772050599, "results": "55", "hashOfConfig": "33"}, {"size": 10296, "mtime": 1751766337855, "results": "56", "hashOfConfig": "33"}, {"size": 7765, "mtime": 1751766394349, "results": "57", "hashOfConfig": "33"}, {"size": 8153, "mtime": 1751766289475, "results": "58", "hashOfConfig": "33"}, {"size": 28530, "mtime": 1751772083092, "results": "59", "hashOfConfig": "33"}, {"size": 28156, "mtime": 1751770424086, "results": "60", "hashOfConfig": "33"}, {"size": 12049, "mtime": 1751774577076, "results": "61", "hashOfConfig": "33"}, {"size": 5528, "mtime": 1751770056993, "results": "62", "hashOfConfig": "33"}, {"size": 8878, "mtime": 1751766244928, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bng5zs", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\vibe\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AiAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AutoSaveIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\LanguageToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ModuleLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\OutputPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ProgressIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartQuestion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\store\\contextStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\generate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\models\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\validate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page_backup.tsx", ["157"], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartFieldAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\TestAIGeneration.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\lib\\llmProviders.ts", [], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "158", "line": 534, "column": 18, "nodeType": null}, "Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?"]