[{"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\vibe\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AiAssistant.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettings.tsx": "13", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AutoSaveIndicator.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx": "15", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\LanguageToggle.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ModuleLayout.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\OutputPanel.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ProgressIndicator.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartQuestion.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeToggle.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\store\\contextStore.ts": "23", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\generate\\route.ts": "24", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\models\\route.ts": "25", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\validate\\route.ts": "26", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartFieldAssistant.tsx": "28", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\TestAIGeneration.tsx": "29", "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\lib\\llmProviders.ts": "30"}, {"size": 1145, "mtime": 1751756292092, "results": "31", "hashOfConfig": "32"}, {"size": 1281, "mtime": 1751756314810, "results": "33", "hashOfConfig": "32"}, {"size": 4711, "mtime": 1751752039368, "results": "34", "hashOfConfig": "32"}, {"size": 5406, "mtime": 1751752064185, "results": "35", "hashOfConfig": "32"}, {"size": 11119, "mtime": 1751751211373, "results": "36", "hashOfConfig": "32"}, {"size": 793, "mtime": 1751769279175, "results": "37", "hashOfConfig": "32"}, {"size": 7233, "mtime": 1751752111799, "results": "38", "hashOfConfig": "32"}, {"size": 10485, "mtime": 1751756134293, "results": "39", "hashOfConfig": "32"}, {"size": 4641, "mtime": 1751752012669, "results": "40", "hashOfConfig": "32"}, {"size": 6963, "mtime": 1751752090185, "results": "41", "hashOfConfig": "32"}, {"size": 2582, "mtime": 1751749235443, "results": "42", "hashOfConfig": "32"}, {"size": 6525, "mtime": 1751756272373, "results": "43", "hashOfConfig": "32"}, {"size": 21199, "mtime": 1751761812515, "results": "44", "hashOfConfig": "32"}, {"size": 1584, "mtime": 1751752141042, "results": "45", "hashOfConfig": "32"}, {"size": 3733, "mtime": 1751762560955, "results": "46", "hashOfConfig": "32"}, {"size": 1882, "mtime": 1751755786921, "results": "47", "hashOfConfig": "32"}, {"size": 4031, "mtime": 1751752183700, "results": "48", "hashOfConfig": "32"}, {"size": 8630, "mtime": 1751769132291, "results": "49", "hashOfConfig": "32"}, {"size": 5420, "mtime": 1751751875523, "results": "50", "hashOfConfig": "32"}, {"size": 6100, "mtime": 1751772952346, "results": "51", "hashOfConfig": "32"}, {"size": 1709, "mtime": 1751749793754, "results": "52", "hashOfConfig": "32"}, {"size": 2382, "mtime": 1751755718062, "results": "53", "hashOfConfig": "32"}, {"size": 10350, "mtime": 1751772050599, "results": "54", "hashOfConfig": "32"}, {"size": 10296, "mtime": 1751766337855, "results": "55", "hashOfConfig": "32"}, {"size": 7765, "mtime": 1751766394349, "results": "56", "hashOfConfig": "32"}, {"size": 8153, "mtime": 1751766289475, "results": "57", "hashOfConfig": "32"}, {"size": 28530, "mtime": 1751772083092, "results": "58", "hashOfConfig": "32"}, {"size": 12049, "mtime": 1751774577076, "results": "59", "hashOfConfig": "32"}, {"size": 5528, "mtime": 1751770056993, "results": "60", "hashOfConfig": "32"}, {"size": 8878, "mtime": 1751766244928, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bng5zs", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openai\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\openrouter\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\vibe\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AiAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ApiSettings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\AutoSaveIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\LanguageToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ModuleLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\OutputPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ProgressIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartQuestion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\store\\contextStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\generate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\models\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\validate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartFieldAssistant.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\TestAIGeneration.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\lib\\llmProviders.ts", [], []]