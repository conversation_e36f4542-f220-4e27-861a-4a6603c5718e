"use strict";(()=>{var e={};e.id=549,e.ids=[549],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6943:(e,t,i)=>{i.r(t),i.d(t,{originalPathname:()=>P,patchFetch:()=>x,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var r={};i.r(r),i.d(r,{POST:()=>u});var n=i(9303),o=i(8716),a=i(670),s=i(7070),p=i(2921);async function u(e){try{let t;let{providerId:i,apiKey:r,model:n,messages:o,context:a,fieldName:u,language:g="ar",temperature:m=.7,maxTokens:h=1e3,baseUrl:f}=await e.json();if(!i||!r||!o)return s.NextResponse.json({error:"Provider ID, API key, and messages are required"},{status:400});let P=(0,p.Mh)(i);if(!P)return s.NextResponse.json({error:"Unknown provider"},{status:400});let x=f||P.baseUrl,$={"Content-Type":"application/json",...(0,p.xP)(i)},M=function(e,t,i){let r="ar"===i,n=r?`أنت مساعد ذكي متخصص في مساعدة المستخدمين في بناء سياق منظم ومفصل للمشاريع التقنية والإبداعية.`:"You are an AI assistant specialized in helping users build structured and detailed context for technical and creative projects.",o=r?`السياق الحالي للمشروع: ${JSON.stringify(e,null,2)}`:`Current project context: ${JSON.stringify(e,null,2)}`,a=function(e,t){let i={name:{ar:"المجال المطلوب: اسم المشروع - قدم اقتراحات لأسماء إبداعية ومناسبة للمشروع",en:"Required field: Project name - Provide suggestions for creative and suitable project names"},purpose:{ar:"المجال المطلوب: الغرض من المشروع - اشرح الهدف الرئيسي والقيمة المضافة",en:"Required field: Project purpose - Explain the main goal and added value"},targetUsers:{ar:"المجال المطلوب: المستخدمون المستهدفون - حدد الجمهور المستهدف بدقة",en:"Required field: Target users - Define the target audience precisely"},goals:{ar:"المجال المطلوب: الأهداف - حدد أهداف واضحة وقابلة للقياس",en:"Required field: Goals - Define clear and measurable objectives"},scope:{ar:"المجال المطلوب: نطاق المشروع - حدد حدود وإمكانيات المشروع",en:"Required field: Project scope - Define project boundaries and capabilities"},timeline:{ar:"المجال المطلوب: الجدول الزمني - اقترح خطة زمنية واقعية",en:"Required field: Timeline - Suggest a realistic time plan"},programmingLanguages:{ar:"المجال المطلوب: لغات البرمجة - اقترح أفضل لغات البرمجة للمشروع",en:"Required field: Programming languages - Suggest the best programming languages for the project"},frameworks:{ar:"المجال المطلوب: الأطر التقنية - اقترح أفضل الأطر والمكتبات",en:"Required field: Frameworks - Suggest the best frameworks and libraries"},databases:{ar:"المجال المطلوب: قواعد البيانات - اقترح أنسب قواعد البيانات",en:"Required field: Databases - Suggest the most suitable databases"}}[e];return i?t?i.ar:i.en:t?`المجال المطلوب: ${e} - قدم محتوى مفيد ومناسب لهذا المجال`:`Required field: ${e} - Provide helpful and appropriate content for this field`}(t,r),s=r?`
تعليمات مهمة:
1. قدم إجابة مفيدة ومنظمة ومفصلة
2. استخدم المعلومات المتوفرة في السياق لتحسين إجابتك
3. اجعل الإجابة عملية وقابلة للتطبيق
4. استخدم اللغة العربية الواضحة والمهنية
5. نظم الإجابة في نقاط أو فقرات حسب الحاجة
6. تأكد من أن الإجابة تتناسب مع طبيعة المشروع المذكور في السياق
`:`
Important instructions:
1. Provide a helpful, organized, and detailed response
2. Use the available context information to improve your answer
3. Make the response practical and actionable
4. Use clear and professional English
5. Organize the response in points or paragraphs as needed
6. Ensure the response fits the nature of the project mentioned in the context
`;return`${n}

${o}

${a}

${s}`}(a,u,g),v=[{role:"system",content:M},...o];switch(i){case"openai":case"openrouter":case"deepseek":case"groq":default:t=await c(x,r,$,n,v,m,h);break;case"anthropic":t=await l(x,r,$,n,v,m,h);break;case"google":t=await d(x,r,$,n,v,m,h)}return s.NextResponse.json(t)}catch(e){return console.error("Generation error:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(e,t,i,r,n,o,a){try{let s=await fetch(`${e}/chat/completions`,{method:"POST",headers:{...i,Authorization:`Bearer ${t}`},body:JSON.stringify({model:r,messages:n,temperature:o,max_tokens:a})});if(!s.ok){let e=await s.text();throw Error(`API error: ${s.status} - ${e}`)}let p=await s.json(),u=p.choices?.[0]?.message?.content||"";return{success:!0,content:u,usage:p.usage}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function l(e,t,i,r,n,o,a){try{let s=n.filter(e=>"system"!==e.role),p=n.find(e=>"system"===e.role)?.content||"",u=await fetch(`${e}/messages`,{method:"POST",headers:{...i,"x-api-key":t},body:JSON.stringify({model:r,max_tokens:a,temperature:o,system:p,messages:s})});if(!u.ok){let e=await u.text();throw Error(`API error: ${u.status} - ${e}`)}let c=await u.json(),l=c.content?.[0]?.text||"";return{success:!0,content:l,usage:c.usage}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function d(e,t,i,r,n,o,a){try{let s=n.map(e=>({role:"assistant"===e.role?"model":"user",parts:[{text:e.content}]})),p=await fetch(`${e}/models/${r}:generateContent?key=${t}`,{method:"POST",headers:i,body:JSON.stringify({contents:s,generationConfig:{temperature:o,maxOutputTokens:a}})});if(!p.ok){let e=await p.text();throw Error(`API error: ${p.status} - ${e}`)}let u=await p.json(),c=u.candidates?.[0]?.content?.parts?.[0]?.text||"";return{success:!0,content:c,usage:u.usageMetadata}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}let g=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/llm/generate/route",pathname:"/api/llm/generate",filename:"route",bundlePath:"app/api/llm/generate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\generate\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:h,serverHooks:f}=g,P="/api/llm/generate/route";function x(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}},2921:(e,t,i)=>{i.d(t,{G4:()=>r,Mh:()=>n,xP:()=>o});let r=[{id:"openai",name:"OpenAI",icon:"\uD83E\uDD16",description:"GPT models from OpenAI - Industry leading language models",baseUrl:"https://api.openai.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:128e3,models:[{id:"gpt-4o",name:"GPT-4o",description:"Most advanced multimodal model",contextLength:128e3,pricing:"$5/1M input, $15/1M output",inputPrice:5,outputPrice:15},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster and more affordable",contextLength:128e3,pricing:"$0.15/1M input, $0.6/1M output",inputPrice:.15,outputPrice:.6},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"High performance model",contextLength:128e3,pricing:"$10/1M input, $30/1M output",inputPrice:10,outputPrice:30},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Fast and efficient",contextLength:16385,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"anthropic",name:"Anthropic",icon:"\uD83E\uDDE0",description:"Claude models from Anthropic - Advanced reasoning capabilities",baseUrl:"https://api.anthropic.com/v1",apiKeyPlaceholder:"sk-ant-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"anthropic-version":"2023-06-01"},models:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Most intelligent model",contextLength:2e5,pricing:"$3/1M input, $15/1M output",inputPrice:3,outputPrice:15},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fastest model",contextLength:2e5,pricing:"$0.25/1M input, $1.25/1M output",inputPrice:.25,outputPrice:1.25},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Most powerful model",contextLength:2e5,pricing:"$15/1M input, $75/1M output",inputPrice:15,outputPrice:75}]},{id:"google",name:"Google AI",icon:"\uD83D\uDD0D",description:"Gemini models from Google - Multimodal AI capabilities",baseUrl:"https://generativelanguage.googleapis.com/v1beta",apiKeyPlaceholder:"AIza...",isActive:!0,supportsStreaming:!0,maxTokens:2e6,models:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Advanced reasoning with 2M context",contextLength:2e6,pricing:"$1.25/1M input, $5/1M output",inputPrice:1.25,outputPrice:5},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,pricing:"$0.075/1M input, $0.3/1M output",inputPrice:.075,outputPrice:.3},{id:"gemini-pro",name:"Gemini Pro",description:"Balanced performance",contextLength:32768,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"openrouter",name:"OpenRouter",icon:"\uD83D\uDD00",description:"Access to multiple models via OpenRouter - One API for all models",baseUrl:"https://openrouter.ai/api/v1",apiKeyPlaceholder:"sk-or-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"HTTP-Referer":process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000","X-Title":"ContextKit"},models:[{id:"openai/gpt-4o",name:"GPT-4o (via OpenRouter)",description:"OpenAI GPT-4o through OpenRouter",contextLength:128e3,pricing:"Variable pricing"},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (via OpenRouter)",description:"Anthropic Claude through OpenRouter",contextLength:2e5,pricing:"Variable pricing"},{id:"google/gemini-pro-1.5",name:"Gemini Pro 1.5 (via OpenRouter)",description:"Google Gemini through OpenRouter",contextLength:1e6,pricing:"Variable pricing"},{id:"meta-llama/llama-3.1-405b-instruct",name:"Llama 3.1 405B (via OpenRouter)",description:"Meta Llama through OpenRouter",contextLength:131072,pricing:"Variable pricing"}]},{id:"deepseek",name:"DeepSeek",icon:"\uD83C\uDF0A",description:"DeepSeek models - Efficient and cost-effective AI",baseUrl:"https://api.deepseek.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose conversational AI",contextLength:32768,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Specialized for code generation",contextLength:16384,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28}]},{id:"groq",name:"Groq",icon:"⚡",description:"Groq - Ultra-fast inference with GroqChip technology",baseUrl:"https://api.groq.com/openai/v1",apiKeyPlaceholder:"gsk_...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Meta Llama 3.1 70B on Groq",contextLength:131072,pricing:"$0.59/1M input, $0.79/1M output",inputPrice:.59,outputPrice:.79},{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Meta Llama 3.1 8B on Groq",contextLength:131072,pricing:"$0.05/1M input, $0.08/1M output",inputPrice:.05,outputPrice:.08},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Mistral Mixtral 8x7B on Groq",contextLength:32768,pricing:"$0.24/1M input, $0.24/1M output",inputPrice:.24,outputPrice:.24}]}];function n(e){return r.find(t=>t.id===e)}function o(e){let t=n(e);return t?.headers||{}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[948,972],()=>i(6943));module.exports=r})();