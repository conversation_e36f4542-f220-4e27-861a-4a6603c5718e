globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/project-definition/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"231":{"*":{"id":"9404","name":"*","chunks":[],"async":false}},"1343":{"*":{"id":"4759","name":"*","chunks":[],"async":false}},"2941":{"*":{"id":"1697","name":"*","chunks":[],"async":false}},"3146":{"*":{"id":"1398","name":"*","chunks":[],"async":false}},"4706":{"*":{"id":"9533","name":"*","chunks":[],"async":false}},"5101":{"*":{"id":"2835","name":"*","chunks":[],"async":false}},"5324":{"*":{"id":"1868","name":"*","chunks":[],"async":false}},"5751":{"*":{"id":"2994","name":"*","chunks":[],"async":false}},"6080":{"*":{"id":"4433","name":"*","chunks":[],"async":false}},"6130":{"*":{"id":"9727","name":"*","chunks":[],"async":false}},"6513":{"*":{"id":"6114","name":"*","chunks":[],"async":false}},"7843":{"*":{"id":"6886","name":"*","chunks":[],"async":false}},"8134":{"*":{"id":"1522","name":"*","chunks":[],"async":false}},"8212":{"*":{"id":"4497","name":"*","chunks":[],"async":false}},"8435":{"*":{"id":"2980","name":"*","chunks":[],"async":false}},"8546":{"*":{"id":"4609","name":"*","chunks":[],"async":false}},"9275":{"*":{"id":"9671","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":5751,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":5751,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":6513,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":6513,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6130,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6130,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":9275,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":9275,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":5324,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":5324,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1343,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1343,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\globals.css":{"id":3054,"name":"*","chunks":["185","static/chunks/app/layout-dae2a31305e77395.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\ThemeProvider.tsx":{"id":8546,"name":"*","chunks":["185","static/chunks/app/layout-dae2a31305e77395.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\styles\\glass-effects.css":{"id":791,"name":"*","chunks":["185","static/chunks/app/layout-dae2a31305e77395.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\context-map\\page.tsx":{"id":7843,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx":{"id":3146,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx":{"id":4706,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\legal-risk\\page.tsx":{"id":2941,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page.tsx":{"id":8212,"name":"*","chunks":["333","static/chunks/333-9b841a17a9a04d9d.js","101","static/chunks/101-95bbe9ad3e085825.js","931","static/chunks/app/page-a0ad2c4733035dbd.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx":{"id":6080,"name":"*","chunks":["333","static/chunks/333-9b841a17a9a04d9d.js","101","static/chunks/101-95bbe9ad3e085825.js","879","static/chunks/879-df9070c7ec9381d7.js","532","static/chunks/app/project-definition/page-3d5df3d3f82b58e8.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\technical-layer\\page.tsx":{"id":8435,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\settings\\page.tsx":{"id":8134,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\client\\link.js":{"id":231,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":231,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\Header.tsx":{"id":5101,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\":[],"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout":["static/css/d86256fb2c3783b1.css"],"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page":[]}}