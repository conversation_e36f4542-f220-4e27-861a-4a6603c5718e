{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/context-map", "regex": "^/context\\-map(?:/)?$", "routeKeys": {}, "namedRegex": "^/context\\-map(?:/)?$"}, {"page": "/emotional-tone", "regex": "^/emotional\\-tone(?:/)?$", "routeKeys": {}, "namedRegex": "^/emotional\\-tone(?:/)?$"}, {"page": "/final-preview", "regex": "^/final\\-preview(?:/)?$", "routeKeys": {}, "namedRegex": "^/final\\-preview(?:/)?$"}, {"page": "/legal-risk", "regex": "^/legal\\-risk(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal\\-risk(?:/)?$"}, {"page": "/project-definition", "regex": "^/project\\-definition(?:/)?$", "routeKeys": {}, "namedRegex": "^/project\\-definition(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/technical-layer", "regex": "^/technical\\-layer(?:/)?$", "routeKeys": {}, "namedRegex": "^/technical\\-layer(?:/)?$"}, {"page": "/vibe", "regex": "^/vibe(?:/)?$", "routeKeys": {}, "namedRegex": "^/vibe(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}