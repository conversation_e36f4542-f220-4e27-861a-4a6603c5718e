"use strict";(()=>{var e={};e.id=285,e.ids=[285],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8488:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>A,requestAsyncStorage:()=>f,routeModule:()=>P,serverHooks:()=>k,staticGenerationAsyncStorage:()=>$});var i={};r.r(i),r.d(i,{POST:()=>c});var n=r(9303),o=r(8716),a=r(670),s=r(7070),p=r(2921);async function c(e){try{let t;let{providerId:r,apiKey:i,baseUrl:n}=await e.json();if(!r||!i)return s.NextResponse.json({valid:!1,error:"Provider ID and API key are required"},{status:400});let o=(0,p.Mh)(r);if(!o)return s.NextResponse.json({valid:!1,error:"Unknown provider"},{status:400});let a=n||o.baseUrl,c={"Content-Type":"application/json",...(0,p.xP)(r)};switch(r){case"openai":t=await u(i,a,c);break;case"anthropic":t=await d(i,a,c);break;case"google":t=await l(i,a,c);break;case"openrouter":t=await m(i,a,c);break;case"deepseek":t=await g(i,a,c);break;case"groq":t=await h(i,a,c);break;default:t=await v(i,a,c)}return s.NextResponse.json(t)}catch(e){return console.error("Validation error:",e),s.NextResponse.json({valid:!1,error:"Internal server error"},{status:500})}}async function u(e,t,r){try{let i=await fetch(`${t}/models`,{method:"GET",headers:{...r,Authorization:`Bearer ${e}`}});if(i.ok){let e=await i.json();return{valid:!0,models:e.data?.map(e=>e.id)||[],message:"API key is valid"}}{let e=await i.text();return{valid:!1,error:`OpenAI API error: ${i.status} - ${e}`}}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function d(e,t,r){try{let i=await fetch(`${t}/messages`,{method:"POST",headers:{...r,"x-api-key":e},body:JSON.stringify({model:"claude-3-5-haiku-20241022",max_tokens:1,messages:[{role:"user",content:"Hi"}]})});if(i.ok||400===i.status)return{valid:!0,message:"API key is valid"};{let e=await i.text();return{valid:!1,error:`Anthropic API error: ${i.status} - ${e}`}}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function l(e,t,r){try{let i=await fetch(`${t}/models?key=${e}`,{method:"GET",headers:r});if(i.ok){let e=await i.json();return{valid:!0,models:e.models?.map(e=>e.name.replace("models/",""))||[],message:"API key is valid"}}{let e=await i.text();return{valid:!1,error:`Google API error: ${i.status} - ${e}`}}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function m(e,t,r){try{let i=await fetch(`${t}/models`,{method:"GET",headers:{...r,Authorization:`Bearer ${e}`}});if(i.ok){let e=await i.json();return{valid:!0,models:e.data?.map(e=>e.id)||[],message:"API key is valid"}}{let e=await i.text();return{valid:!1,error:`OpenRouter API error: ${i.status} - ${e}`}}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function g(e,t,r){try{let i=await fetch(`${t}/models`,{method:"GET",headers:{...r,Authorization:`Bearer ${e}`}});if(i.ok){let e=await i.json();return{valid:!0,models:e.data?.map(e=>e.id)||[],message:"API key is valid"}}{let e=await i.text();return{valid:!1,error:`DeepSeek API error: ${i.status} - ${e}`}}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function h(e,t,r){try{let i=await fetch(`${t}/models`,{method:"GET",headers:{...r,Authorization:`Bearer ${e}`}});if(i.ok){let e=await i.json();return{valid:!0,models:e.data?.map(e=>e.id)||[],message:"API key is valid"}}{let e=await i.text();return{valid:!1,error:`Groq API error: ${i.status} - ${e}`}}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}async function v(e,t,r){try{for(let i of["/models","/v1/models"])try{let n=await fetch(`${t}${i}`,{method:"GET",headers:{...r,Authorization:`Bearer ${e}`}});if(n.ok){let e=await n.json();return{valid:!0,models:e.data?.map(e=>e.id)||[],message:"API key is valid"}}}catch(e){continue}return{valid:!1,error:"Could not validate API key with standard endpoints"}}catch(e){return{valid:!1,error:`Connection error: ${e instanceof Error?e.message:"Unknown error"}`}}}let P=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/llm/validate/route",pathname:"/api/llm/validate",filename:"route",bundlePath:"app/api/llm/validate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\validate\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:$,serverHooks:k}=P,x="/api/llm/validate/route";function A(){return(0,a.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:$})}},2921:(e,t,r)=>{r.d(t,{G4:()=>i,Mh:()=>n,xP:()=>o});let i=[{id:"openai",name:"OpenAI",icon:"\uD83E\uDD16",description:"GPT models from OpenAI - Industry leading language models",baseUrl:"https://api.openai.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:128e3,models:[{id:"gpt-4o",name:"GPT-4o",description:"Most advanced multimodal model",contextLength:128e3,pricing:"$5/1M input, $15/1M output",inputPrice:5,outputPrice:15},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster and more affordable",contextLength:128e3,pricing:"$0.15/1M input, $0.6/1M output",inputPrice:.15,outputPrice:.6},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"High performance model",contextLength:128e3,pricing:"$10/1M input, $30/1M output",inputPrice:10,outputPrice:30},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Fast and efficient",contextLength:16385,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"anthropic",name:"Anthropic",icon:"\uD83E\uDDE0",description:"Claude models from Anthropic - Advanced reasoning capabilities",baseUrl:"https://api.anthropic.com/v1",apiKeyPlaceholder:"sk-ant-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"anthropic-version":"2023-06-01"},models:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Most intelligent model",contextLength:2e5,pricing:"$3/1M input, $15/1M output",inputPrice:3,outputPrice:15},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fastest model",contextLength:2e5,pricing:"$0.25/1M input, $1.25/1M output",inputPrice:.25,outputPrice:1.25},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Most powerful model",contextLength:2e5,pricing:"$15/1M input, $75/1M output",inputPrice:15,outputPrice:75}]},{id:"google",name:"Google AI",icon:"\uD83D\uDD0D",description:"Gemini models from Google - Multimodal AI capabilities",baseUrl:"https://generativelanguage.googleapis.com/v1beta",apiKeyPlaceholder:"AIza...",isActive:!0,supportsStreaming:!0,maxTokens:2e6,models:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Advanced reasoning with 2M context",contextLength:2e6,pricing:"$1.25/1M input, $5/1M output",inputPrice:1.25,outputPrice:5},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,pricing:"$0.075/1M input, $0.3/1M output",inputPrice:.075,outputPrice:.3},{id:"gemini-pro",name:"Gemini Pro",description:"Balanced performance",contextLength:32768,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"openrouter",name:"OpenRouter",icon:"\uD83D\uDD00",description:"Access to multiple models via OpenRouter - One API for all models",baseUrl:"https://openrouter.ai/api/v1",apiKeyPlaceholder:"sk-or-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"HTTP-Referer":process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000","X-Title":"ContextKit"},models:[{id:"openai/gpt-4o",name:"GPT-4o (via OpenRouter)",description:"OpenAI GPT-4o through OpenRouter",contextLength:128e3,pricing:"Variable pricing"},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (via OpenRouter)",description:"Anthropic Claude through OpenRouter",contextLength:2e5,pricing:"Variable pricing"},{id:"google/gemini-pro-1.5",name:"Gemini Pro 1.5 (via OpenRouter)",description:"Google Gemini through OpenRouter",contextLength:1e6,pricing:"Variable pricing"},{id:"meta-llama/llama-3.1-405b-instruct",name:"Llama 3.1 405B (via OpenRouter)",description:"Meta Llama through OpenRouter",contextLength:131072,pricing:"Variable pricing"}]},{id:"deepseek",name:"DeepSeek",icon:"\uD83C\uDF0A",description:"DeepSeek models - Efficient and cost-effective AI",baseUrl:"https://api.deepseek.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose conversational AI",contextLength:32768,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Specialized for code generation",contextLength:16384,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28}]},{id:"groq",name:"Groq",icon:"⚡",description:"Groq - Ultra-fast inference with GroqChip technology",baseUrl:"https://api.groq.com/openai/v1",apiKeyPlaceholder:"gsk_...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Meta Llama 3.1 70B on Groq",contextLength:131072,pricing:"$0.59/1M input, $0.79/1M output",inputPrice:.59,outputPrice:.79},{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Meta Llama 3.1 8B on Groq",contextLength:131072,pricing:"$0.05/1M input, $0.08/1M output",inputPrice:.05,outputPrice:.08},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Mistral Mixtral 8x7B on Groq",contextLength:32768,pricing:"$0.24/1M input, $0.24/1M output",inputPrice:.24,outputPrice:.24}]}];function n(e){return i.find(t=>t.id===e)}function o(e){let t=n(e);return t?.headers||{}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[948,972],()=>r(8488));module.exports=i})();