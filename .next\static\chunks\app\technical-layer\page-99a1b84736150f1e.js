(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[391],{151:function(e,a,t){Promise.resolve().then(t.bind(t,8435))},8435:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return l}});var r=t(7437),i=t(5924),n=t(5097),o=t(6358),s=t(4792);function l(){let{technicalLayer:e,updateTechnicalLayer:a}=(0,s.l)(),t=(e,t)=>{a({[e]:t})};return(0,r.jsx)(i.Z,{title:"Technical Layer",titleAr:"الطبقة التقنية",subtitle:"Define the technical architecture and tools for your AI project",subtitleAr:"حدد الهندسة التقنية والأدوات لمشروع الذكاء الاصطناعي",emoji:"⚙️",moduleKey:"technical-layer",backLink:{href:"/emotional-tone",label:"← Back to Emotional Tone",labelAr:"← العودة للنبرة العاطفية"},nextLink:{href:"/legal-risk",label:"Next: Legal & Privacy →",labelAr:"التالي: القانونية والخصوصية ←"},rightPanel:(0,r.jsx)(o.Z,{moduleData:e,moduleName:"Technical Layer",moduleNameAr:"الطبقة التقنية"}),children:(0,r.jsx)("div",{className:"space-y-6",children:[{id:"programmingLanguages",question:"What programming languages will be used in your project?",questionAr:"ما هي لغات البرمجة التي ستُستخدم في مشروعك؟",placeholder:"e.g., Python, JavaScript, TypeScript, Java, C#...",placeholderAr:"مثال: Python، JavaScript، TypeScript، Java، C#...",type:"text",aiSuggestion:"Consider the AI/ML libraries available, team expertise, and performance requirements.",aiSuggestionAr:"فكر في مكتبات الذكاء الاصطناعي المتاحة وخبرة الفريق ومتطلبات الأداء.",promptTemplate:'Help me choose the best programming languages for this AI project: "{answer}". Consider scalability and ecosystem.'},{id:"frameworks",question:"What frameworks and libraries will you use?",questionAr:"ما هي الأطر والمكتبات التي ستستخدمها؟",placeholder:"e.g., TensorFlow, PyTorch, React, Next.js, FastAPI, Django...",placeholderAr:"مثال: TensorFlow، PyTorch، React، Next.js، FastAPI، Django...",aiSuggestion:"Choose frameworks that align with your project goals and team capabilities.",aiSuggestionAr:"اختر الأطر التي تتماشى مع أهداف مشروعك وقدرات فريقك.",promptTemplate:'Analyze this technology stack: "{answer}". Suggest optimizations and alternatives.'},{id:"llmModels",question:"Which LLM models and AI services will you integrate?",questionAr:"ما هي نماذج اللغة الكبيرة وخدمات الذكاء الاصطناعي التي ستدمجها؟",placeholder:"e.g., GPT-4, Claude, Gemini, Local models, OpenAI API, Anthropic API...",placeholderAr:"مثال: GPT-4، Claude، Gemini، نماذج محلية، OpenAI API، Anthropic API...",aiSuggestion:"Consider cost, performance, privacy requirements, and specific capabilities needed.",aiSuggestionAr:"فكر في التكلفة والأداء ومتطلبات الخصوصية والقدرات المحددة المطلوبة.",promptTemplate:'Help me design an LLM integration strategy for: "{answer}". Include fallback options.'},{id:"databases",question:"What databases and data storage solutions will you use?",questionAr:"ما هي قواعد البيانات وحلول تخزين البيانات التي ستستخدمها؟",placeholder:"e.g., PostgreSQL, MongoDB, Redis, Vector databases, Cloud storage...",placeholderAr:"مثال: PostgreSQL، MongoDB، Redis، قواعد بيانات المتجهات، التخزين السحابي...",aiSuggestion:"Consider data types, scalability, consistency requirements, and AI-specific needs.",aiSuggestionAr:"فكر في أنواع البيانات وقابلية التوسع ومتطلبات الاتساق والاحتياجات الخاصة بالذكاء الاصطناعي.",promptTemplate:'Design a data architecture for: "{answer}". Include backup and scaling strategies.'},{id:"apis",question:"What APIs and external services will you integrate?",questionAr:"ما هي واجهات برمجة التطبيقات والخدمات الخارجية التي ستدمجها؟",placeholder:"e.g., REST APIs, GraphQL, Third-party services, Payment gateways...",placeholderAr:"مثال: REST APIs، GraphQL، خدمات طرف ثالث، بوابات الدفع...",aiSuggestion:"Plan for API rate limits, authentication, error handling, and monitoring.",aiSuggestionAr:"خطط لحدود معدل API والمصادقة ومعالجة الأخطاء والمراقبة.",promptTemplate:'Create an API integration plan for: "{answer}". Include security and reliability measures.'},{id:"infrastructure",question:"What infrastructure and deployment strategy will you use?",questionAr:"ما هي البنية التحتية واستراتيجية النشر التي ستستخدمها؟",placeholder:"e.g., AWS, Google Cloud, Azure, Docker, Kubernetes, Serverless...",placeholderAr:"مثال: AWS، Google Cloud، Azure، Docker، Kubernetes، Serverless...",aiSuggestion:"Consider scalability, cost optimization, security, and maintenance requirements.",aiSuggestionAr:"فكر في قابلية التوسع وتحسين التكلفة والأمان ومتطلبات الصيانة.",promptTemplate:'Design an infrastructure architecture for: "{answer}". Include monitoring and scaling plans.'}].map(a=>(0,r.jsx)(n.Z,{id:a.id,question:a.question,questionAr:a.questionAr,placeholder:a.placeholder,placeholderAr:a.placeholderAr,value:e[a.id]||"",onChange:e=>t(a.id,e),type:a.type,aiSuggestion:a.aiSuggestion,aiSuggestionAr:a.aiSuggestionAr,promptTemplate:a.promptTemplate},a.id))})})}}},function(e){e.O(0,[333,101,879,971,23,744],function(){return e(e.s=151)}),_N_E=e.O()}]);