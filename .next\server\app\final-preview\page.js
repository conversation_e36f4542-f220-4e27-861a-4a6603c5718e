(()=>{var e={};e.id=421,e.ids=[421],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5785:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),a(4519),a(6157),a(5866);var r=a(3191),s=a(8716),n=a(7922),i=a.n(n),o=a(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c=["",{children:["final-preview",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4519)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,6157)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\final-preview\\page.tsx"],m="/final-preview/page",x={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/final-preview/page",pathname:"/final-preview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8466:(e,t,a)=>{Promise.resolve().then(a.bind(a,1398))},1398:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(326),s=a(7577),n=a(2835),i=a(9190);function o(){let{getAllData:e,currentLanguage:t,outputFormat:a,setOutputFormat:o,projectDefinition:l,contextMap:c,emotionalTone:d,technicalLayer:m,legalRisk:x}=(0,i.l)(),[p,g]=(0,s.useState)(!1),h="ar"===t,u=[{key:"projectDefinition",name:"Project Definition",nameAr:"تعريف المشروع",emoji:"\uD83C\uDFAF",data:l,href:"/project-definition"},{key:"contextMap",name:"Context Map",nameAr:"خريطة السياق",emoji:"\uD83D\uDDFA️",data:c,href:"/context-map"},{key:"emotionalTone",name:"Emotional Tone",nameAr:"النبرة العاطفية",emoji:"✨",data:d,href:"/emotional-tone"},{key:"technicalLayer",name:"Technical Layer",nameAr:"الطبقة التقنية",emoji:"⚙️",data:m,href:"/technical-layer"},{key:"legalRisk",name:"Legal & Privacy",nameAr:"القانونية والخصوصية",emoji:"\uD83D\uDD12",data:x,href:"/legal-risk"}],f=()=>{let r=e();if("markdown"===a){let e=`# ContextKit - Complete AI Project Context

`;return e+=`Generated on: ${new Date().toLocaleDateString()}

`,u.forEach(t=>{let a=h?t.nameAr:t.name;e+=`## ${t.emoji} ${a}

`,Object.entries(t.data).forEach(([t,a])=>{if(a&&"string"==typeof a&&a.trim()){let r=t.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());e+=`### ${r}
${a}

`}})}),e}if("json"===a)return JSON.stringify({contextKit:{metadata:{generatedAt:new Date().toISOString(),language:t,version:"1.0"},modules:r}},null,2);if("html"===a){let e=`<!DOCTYPE html>
<html lang="${t}">
<head>
`;return e+=`  <meta charset="UTF-8">
  <title>ContextKit - AI Project Context</title>
  <style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}</style>
</head>
<body>
  <h1>🧠 ContextKit - Complete AI Project Context</h1>
  <p><em>Generated on: ${new Date().toLocaleDateString()}</em></p>

`,u.forEach(t=>{let a=h?t.nameAr:t.name;e+=`  <section>
    <h2>${t.emoji} ${a}</h2>
`,Object.entries(t.data).forEach(([t,a])=>{if(a&&"string"==typeof a&&a.trim()){let r=t.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());e+=`    <h3>${r}</h3>
    <p>${a}</p>
`}}),e+=`  </section>

`}),e+=`</body>
</html>`}return""},b=async()=>{let e=f();await navigator.clipboard.writeText(e),g(!0),setTimeout(()=>g(!1),2e3)},y=u.some(e=>Object.values(e.data).some(e=>e&&"string"==typeof e&&e.trim()));return r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-900 dark:to-gray-800",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(n.default,{title:h?"المعاينة النهائية":"Final Preview",subtitle:h?"راجع واستخرج السياق الكامل لمشروعك":"Review and export your complete project context",emoji:"\uD83D\uDCCB",backLink:{href:"/emotional-tone",label:h?"← العودة للنبرة العاطفية":"← Back to Emotional Tone"}}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto mb-12",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center",children:h?"نظرة عامة على المحاور":"Modules Overview"}),r.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map(e=>{let t=Object.values(e.data).some(e=>e&&"string"==typeof e&&e.trim());return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("span",{className:"text-2xl mr-3",children:e.emoji}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:h?e.nameAr:e.name})]}),r.jsx("div",{className:`w-3 h-3 rounded-full ${t?"bg-green-500":"bg-gray-300"}`})]}),r.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:t?h?"مكتمل":"Completed":h?"غير مكتمل":"Incomplete"}),(0,r.jsxs)("a",{href:e.href,className:"text-blue-600 dark:text-blue-400 hover:underline text-sm",children:[h?"تعديل":"Edit"," →"]})]},e.key)})})]}),y&&r.jsx("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:h?"تصدير السياق الكامل":"Export Complete Context"}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("div",{className:"flex space-x-2",children:["markdown","html","json"].map(e=>r.jsx("button",{onClick:()=>o(e),className:`px-4 py-2 rounded-lg transition-colors ${a===e?"bg-blue-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"}`,children:e.toUpperCase()},e))}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("button",{onClick:b,className:"flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",children:[r.jsx("span",{className:"mr-2",children:"\uD83D\uDCCB"}),p?h?"تم النسخ!":"Copied!":h?"نسخ الكل":"Copy All"]}),(0,r.jsxs)("button",{onClick:()=>{let e=f(),t=`contextkit-complete.${"json"===a?"json":"html"===a?"html":"md"}`,r=new Blob([e],{type:"text/plain"}),s=URL.createObjectURL(r),n=document.createElement("a");n.href=s,n.download=t,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(s)},className:"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[r.jsx("span",{className:"mr-2",children:"\uD83D\uDCBE"}),h?"تحميل":"Download"]})]})]}),r.jsx("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-6 max-h-96 overflow-y-auto",children:r.jsx("pre",{className:`text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono ${h?"text-right":"text-left"}`,children:f()})})]})}),!y&&r.jsx("div",{className:"max-w-2xl mx-auto text-center",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-12",children:[r.jsx("span",{className:"text-6xl mb-4 block",children:"\uD83D\uDCDD"}),r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:h?"لا توجد بيانات للمعاينة":"No Data to Preview"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:h?"ابدأ بملء المحاور المختلفة لرؤية المعاينة النهائية":"Start filling out the different modules to see the final preview"}),r.jsx("a",{href:"/project-definition",className:"inline-block px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:h?"ابدأ من تعريف المشروع":"Start with Project Definition"})]})})]})})}},4519:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>o});var r=a(8570);let s=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\final-preview\page.tsx`),{__esModule:n,$$typeof:i}=s;s.default;let o=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\final-preview\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[948,471,634,42],()=>a(5785));module.exports=r})();