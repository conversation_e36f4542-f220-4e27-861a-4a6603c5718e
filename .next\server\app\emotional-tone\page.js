(()=>{var e={};e.id=592,e.ids=[592],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1124:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>r.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>m,tree:()=>p}),n(247),n(6157),n(5866);var o=n(3191),a=n(8716),i=n(7922),r=n.n(i),s=n(5231),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);n.d(t,l);let p=["",{children:["emotional-tone",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,247)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,6157)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,5866,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\emotional-tone\\page.tsx"],c="/emotional-tone/page",d={require:n,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/emotional-tone/page",pathname:"/emotional-tone",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},796:(e,t,n)=>{Promise.resolve().then(n.bind(n,9533))},9533:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l});var o=n(326),a=n(2561),i=n(9361),r=n(9740),s=n(9190);function l(){let{emotionalTone:e,updateEmotionalTone:t}=(0,s.l)(),n=(e,n)=>{t({[e]:n})};return o.jsx(a.Z,{title:"Emotional Tone & Experience",titleAr:"النبرة العاطفية والتجربة",subtitle:"Define the personality, tone, and user experience for your AI",subtitleAr:"حدد الشخصية والنبرة وتجربة المستخدم للذكاء الاصطناعي",emoji:"✨",moduleKey:"emotional-tone",backLink:{href:"/context-map",label:"← Back to Context Map",labelAr:"← العودة لخريطة السياق"},nextLink:{href:"/technical-layer",label:"Next: Technical Layer →",labelAr:"التالي: الطبقة التقنية ←"},rightPanel:o.jsx(r.Z,{moduleData:e,moduleName:"Emotional Tone & Experience",moduleNameAr:"النبرة العاطفية والتجربة"}),children:o.jsx("div",{className:"space-y-6",children:[{id:"personality",question:"What personality should your AI system have?",questionAr:"ما هي الشخصية التي يجب أن يتمتع بها نظام الذكاء الاصطناعي؟",placeholder:"e.g., Professional and helpful, Friendly and casual, Formal and authoritative...",placeholderAr:"مثال: مهني ومفيد، ودود وغير رسمي، رسمي وموثوق...",aiSuggestion:"Consider your brand values, target audience expectations, and the context of use.",aiSuggestionAr:"فكر في قيم علامتك التجارية وتوقعات الجمهور المستهدف وسياق الاستخدام.",promptTemplate:'Help me define the ideal AI personality for this description: "{answer}". Suggest specific traits and behaviors.'},{id:"communicationStyle",question:"How should your AI communicate with users?",questionAr:"كيف يجب أن يتواصل الذكاء الاصطناعي مع المستخدمين؟",placeholder:"e.g., Clear and concise, Detailed explanations, Step-by-step guidance...",placeholderAr:"مثال: واضح ومختصر، شروحات مفصلة، إرشادات خطوة بخطوة...",aiSuggestion:"Think about communication preferences, complexity levels, and user expertise.",aiSuggestionAr:"فكر في تفضيلات التواصل ومستويات التعقيد وخبرة المستخدم.",promptTemplate:'Analyze this communication style for an AI system: "{answer}". How can I implement this effectively?'},{id:"userExperience",question:"What kind of user experience do you want to create?",questionAr:"ما نوع تجربة المستخدم التي تريد إنشاؤها؟",placeholder:"e.g., Seamless and intuitive, Educational and informative, Fast and efficient...",placeholderAr:"مثال: سلسة وبديهية، تعليمية وإعلامية، سريعة وفعالة...",aiSuggestion:"Focus on user goals, pain points, and the emotional journey you want to create.",aiSuggestionAr:"ركز على أهداف المستخدم ونقاط الألم والرحلة العاطفية التي تريد إنشاؤها.",promptTemplate:'Help me design a user experience strategy based on: "{answer}". What specific elements should I include?'},{id:"brandVoice",question:"What is your brand voice and messaging approach?",questionAr:"ما هو صوت علامتك التجارية ونهج الرسائل؟",placeholder:"e.g., Innovative and forward-thinking, Trustworthy and reliable, Creative and inspiring...",placeholderAr:"مثال: مبتكر ومتطلع للمستقبل، جدير بالثقة وموثوق، إبداعي وملهم...",aiSuggestion:"Align with your company values and differentiate from competitors.",aiSuggestionAr:"اتماشى مع قيم شركتك وميز نفسك عن المنافسين.",promptTemplate:'Develop a brand voice strategy for: "{answer}". How should this translate into AI interactions?'}].map(t=>o.jsx(i.Z,{id:t.id,question:t.question,questionAr:t.questionAr,placeholder:t.placeholder,placeholderAr:t.placeholderAr,value:e[t.id]||"",onChange:e=>n(t.id,e),aiSuggestion:t.aiSuggestion,aiSuggestionAr:t.aiSuggestionAr,promptTemplate:t.promptTemplate},t.id))})})}},247:(e,t,n)=>{"use strict";n.r(t),n.d(t,{$$typeof:()=>r,__esModule:()=>i,default:()=>s});var o=n(8570);let a=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\emotional-tone\page.tsx`),{__esModule:i,$$typeof:r}=a;a.default;let s=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\emotional-tone\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),o=t.X(0,[948,471,634,42,762],()=>n(1124));module.exports=o})();