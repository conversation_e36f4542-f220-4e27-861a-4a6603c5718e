"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[101],{5101:function(e,t,r){r.d(t,{default:function(){return c}});var a=r(7437),o=r(7138);let i=(0,r(8030).Z)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var n=r(4258),s=r(6393),l=r(8339),d=r(4792);function c(e){let{title:t,subtitle:r,backLink:c,emoji:u}=e,{currentLanguage:g}=(0,d.l)(),p="ar"===g;return(0,a.jsxs)("header",{className:"relative",children:[(0,a.jsx)("div",{className:"fixed top-4 ".concat(p?"left-4":"right-4"," z-50 flex gap-3"),children:(0,a.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(o.default,{href:"/",className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden",title:p?"الصفحة الرئيسية":"Home",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)(i,{className:"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,a.jsxs)(o.default,{href:"/settings",className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden",title:p?"إعدادات API":"API Settings",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)(n.Z,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,a.jsx)(l.Z,{}),(0,a.jsx)(s.Z,{})]})})}),(0,a.jsxs)("div",{className:"text-center mb-8 pt-4",children:[c&&(0,a.jsx)(o.default,{href:c.href,className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors",children:c.label}),(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2",children:[u&&(0,a.jsx)("span",{className:"mr-2",children:u}),t]}),r&&(0,a.jsx)("p",{className:"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:r})]})]})}},8339:function(e,t,r){r.d(t,{Z:function(){return i}});var a=r(7437),o=r(4792);function i(){let{currentLanguage:e,setLanguage:t}=(0,o.l)();return(0,a.jsxs)("button",{onClick:()=>t("ar"===e?"en":"ar"),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"ar"===e?"Switch to English":"التبديل إلى العربية",title:"ar"===e?"Switch to English":"التبديل إلى العربية",children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,a.jsx)("span",{className:"text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ".concat("en"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"),children:"EN"}),(0,a.jsx)("span",{className:"text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ".concat("ar"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"),children:"عر"})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}},8546:function(e,t,r){r.d(t,{F:function(){return s},ThemeProvider:function(){return n}});var a=r(7437),o=r(2265);let i=(0,o.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,o.useState)("light"),[s,l]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(e||t),l(!0)},[]),(0,o.useEffect)(()=>{if(s){let e=document.documentElement;"dark"===r?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",r)}},[r,s]),s)?(0,a.jsx)(i.Provider,{value:{theme:r,toggleTheme:()=>{n(e=>"light"===e?"dark":"light")}},children:t}):(0,a.jsx)(a.Fragment,{children:t})}function s(){let e=(0,o.useContext)(i);return void 0===e?{theme:"light",toggleTheme:()=>{}}:e}},6393:function(e,t,r){r.d(t,{Z:function(){return i}});var a=r(7437),o=r(8546);function i(){let{theme:e,toggleTheme:t}=(0,o.F)();return(0,a.jsxs)("button",{onClick:t,className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"Switch to ".concat("light"===e?"dark":"light"," mode"),title:"Switch to ".concat("light"===e?"dark":"light"," mode"),children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ".concat("light"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),(0,a.jsx)("svg",{className:"w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ".concat("dark"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}},4792:function(e,t,r){r.d(t,{l:function(){return n}});var a=r(903),o=r(9291);let i={projectDefinition:{name:"",purpose:"",targetUsers:"",goals:"",scope:"",timeline:""},contextMap:{timeContext:"",language:"",location:"",culturalContext:"",behavioralAspects:"",environmentalFactors:""},emotionalTone:{personality:"",communicationStyle:"",userExperience:"",brandVoice:"",emotionalIntelligence:"",interactionFlow:""},technicalLayer:{programmingLanguages:"",frameworks:"",llmModels:"",databases:"",apis:"",infrastructure:""},legalRisk:{privacyConcerns:"",dataProtection:"",compliance:"",risks:"",mitigation:"",ethicalConsiderations:""},currentLanguage:"ar",outputFormat:"markdown",apiSettings:{providers:[],globalSettings:{temperature:.7,topP:.9,maxTokens:1e3,timeout:3e4},openaiApiKey:"",openrouterApiKey:"",customModels:[]}},n=(0,a.U)()((0,o.tJ)((e,t)=>({...i,updateProjectDefinition:t=>e(e=>({projectDefinition:{...e.projectDefinition,...t}})),updateContextMap:t=>e(e=>({contextMap:{...e.contextMap,...t}})),updateEmotionalTone:t=>e(e=>({emotionalTone:{...e.emotionalTone,...t}})),updateTechnicalLayer:t=>e(e=>({technicalLayer:{...e.technicalLayer,...t}})),updateLegalRisk:t=>e(e=>({legalRisk:{...e.legalRisk,...t}})),setLanguage:t=>e({currentLanguage:t}),setOutputFormat:t=>e({outputFormat:t}),setApiSettings:t=>e({apiSettings:{...t,providers:t.providers||[]}}),addProvider:t=>e(e=>{let r=e.apiSettings.providers||[];return r.find(e=>e.id===t.id)?(console.warn("Provider with id ".concat(t.id," already exists")),e):{apiSettings:{...e.apiSettings,providers:[...r,t]}}}),updateProvider:(t,r)=>e(e=>({apiSettings:{...e.apiSettings,providers:(e.apiSettings.providers||[]).map(e=>e.id===t?{...e,...r}:e)}})),removeProvider:t=>e(e=>({apiSettings:{...e.apiSettings,providers:(e.apiSettings.providers||[]).filter(e=>e.id!==t),defaultProvider:e.apiSettings.defaultProvider===t?void 0:e.apiSettings.defaultProvider}})),validateProvider:async e=>{let r=t(),a=(r.apiSettings.providers||[]).find(t=>t.id===e);if(!a)return!1;try{r.updateProvider(e,{validationStatus:"pending",errorMessage:void 0});let t=await fetch("/api/llm/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({providerId:a.id,apiKey:a.apiKey,baseUrl:a.baseUrl})}),o=await t.json();if(o.valid)return r.updateProvider(e,{validationStatus:"valid",lastValidated:new Date,errorMessage:void 0,isEnabled:!0}),!0;return r.updateProvider(e,{validationStatus:"invalid",errorMessage:o.error||"Validation failed"}),!1}catch(t){return r.updateProvider(e,{validationStatus:"error",errorMessage:t instanceof Error?t.message:"Unknown error"}),!1}},getProvider:e=>(t().apiSettings.providers||[]).find(t=>t.id===e),getActiveProviders:()=>(t().apiSettings.providers||[]).filter(e=>e.isEnabled),setDefaultProvider:t=>e(e=>({apiSettings:{...e.apiSettings,defaultProvider:t}})),resetAll:()=>e(i),getModuleData:e=>{let r=t();switch(e){case"project":return r.projectDefinition;case"context":return r.contextMap;case"emotional":return r.emotionalTone;case"technical":return r.technicalLayer;case"legal":return r.legalRisk;default:return{}}},getAllData:()=>{let e=t();return{projectDefinition:e.projectDefinition,contextMap:e.contextMap,emotionalTone:e.emotionalTone,technicalLayer:e.technicalLayer,legalRisk:e.legalRisk}}}),{name:"contextkit-storage",version:1}))}}]);