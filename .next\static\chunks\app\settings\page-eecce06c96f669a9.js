(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[938],{3515:function(e,t,r){Promise.resolve().then(r.bind(r,8134))},3274:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});let a=(0,r(8030).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},357:function(e,t,r){"use strict";var a,i;e.exports=(null==(a=r.g.process)?void 0:a.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(8081)},8081:function(e){!function(){var t={229:function(e){var t,r,a,i=e.exports={};function s(){throw Error("setTimeout has not been defined")}function n(){throw Error("clearTimeout has not been defined")}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===s||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:s}catch(e){t=s}try{r="function"==typeof clearTimeout?clearTimeout:n}catch(e){r=n}}();var l=[],d=!1,c=-1;function u(){d&&a&&(d=!1,a.length?l=a.concat(l):c=-1,l.length&&p())}function p(){if(!d){var e=o(u);d=!0;for(var t=l.length;t;){for(a=l,l=[];++c<t;)a&&a[c].run();c=-1,t=l.length}a=null,d=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===n||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function g(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new g(e,t)),1!==l.length||d||o(p)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function a(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},n=!0;try{t[e](s,s.exports,a),n=!1}finally{n&&delete r[e]}return s.exports}a.ab="//";var i=a(229);e.exports=i}()},8134:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return P}});var a=r(7437),i=r(2265),s=r(4792);let n=[{id:"openai",name:"OpenAI",icon:"\uD83E\uDD16",description:"GPT models from OpenAI - Industry leading language models",baseUrl:"https://api.openai.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:128e3,models:[{id:"gpt-4o",name:"GPT-4o",description:"Most advanced multimodal model",contextLength:128e3,pricing:"$5/1M input, $15/1M output",inputPrice:5,outputPrice:15},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster and more affordable",contextLength:128e3,pricing:"$0.15/1M input, $0.6/1M output",inputPrice:.15,outputPrice:.6},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"High performance model",contextLength:128e3,pricing:"$10/1M input, $30/1M output",inputPrice:10,outputPrice:30},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Fast and efficient",contextLength:16385,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"anthropic",name:"Anthropic",icon:"\uD83E\uDDE0",description:"Claude models from Anthropic - Advanced reasoning capabilities",baseUrl:"https://api.anthropic.com/v1",apiKeyPlaceholder:"sk-ant-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"anthropic-version":"2023-06-01"},models:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Most intelligent model",contextLength:2e5,pricing:"$3/1M input, $15/1M output",inputPrice:3,outputPrice:15},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fastest model",contextLength:2e5,pricing:"$0.25/1M input, $1.25/1M output",inputPrice:.25,outputPrice:1.25},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Most powerful model",contextLength:2e5,pricing:"$15/1M input, $75/1M output",inputPrice:15,outputPrice:75}]},{id:"google",name:"Google AI",icon:"\uD83D\uDD0D",description:"Gemini models from Google - Multimodal AI capabilities",baseUrl:"https://generativelanguage.googleapis.com/v1beta",apiKeyPlaceholder:"AIza...",isActive:!0,supportsStreaming:!0,maxTokens:2e6,models:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Advanced reasoning with 2M context",contextLength:2e6,pricing:"$1.25/1M input, $5/1M output",inputPrice:1.25,outputPrice:5},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,pricing:"$0.075/1M input, $0.3/1M output",inputPrice:.075,outputPrice:.3},{id:"gemini-pro",name:"Gemini Pro",description:"Balanced performance",contextLength:32768,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"openrouter",name:"OpenRouter",icon:"\uD83D\uDD00",description:"Access to multiple models via OpenRouter - One API for all models",baseUrl:"https://openrouter.ai/api/v1",apiKeyPlaceholder:"sk-or-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"HTTP-Referer":r(357).env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000","X-Title":"ContextKit"},models:[{id:"openai/gpt-4o",name:"GPT-4o (via OpenRouter)",description:"OpenAI GPT-4o through OpenRouter",contextLength:128e3,pricing:"Variable pricing"},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (via OpenRouter)",description:"Anthropic Claude through OpenRouter",contextLength:2e5,pricing:"Variable pricing"},{id:"google/gemini-pro-1.5",name:"Gemini Pro 1.5 (via OpenRouter)",description:"Google Gemini through OpenRouter",contextLength:1e6,pricing:"Variable pricing"},{id:"meta-llama/llama-3.1-405b-instruct",name:"Llama 3.1 405B (via OpenRouter)",description:"Meta Llama through OpenRouter",contextLength:131072,pricing:"Variable pricing"}]},{id:"deepseek",name:"DeepSeek",icon:"\uD83C\uDF0A",description:"DeepSeek models - Efficient and cost-effective AI",baseUrl:"https://api.deepseek.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose conversational AI",contextLength:32768,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Specialized for code generation",contextLength:16384,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28}]},{id:"groq",name:"Groq",icon:"⚡",description:"Groq - Ultra-fast inference with GroqChip technology",baseUrl:"https://api.groq.com/openai/v1",apiKeyPlaceholder:"gsk_...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Meta Llama 3.1 70B on Groq",contextLength:131072,pricing:"$0.59/1M input, $0.79/1M output",inputPrice:.59,outputPrice:.79},{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Meta Llama 3.1 8B on Groq",contextLength:131072,pricing:"$0.05/1M input, $0.08/1M output",inputPrice:.05,outputPrice:.08},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Mistral Mixtral 8x7B on Groq",contextLength:32768,pricing:"$0.24/1M input, $0.24/1M output",inputPrice:.24,outputPrice:.24}]}];function o(e){return n.find(t=>t.id===e)}var l=r(3274),d=r(8030);let c=(0,d.Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),u=(0,d.Z)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),p=(0,d.Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),g=(0,d.Z)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),m=(0,d.Z)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var x=r(4258);let h=(0,d.Z)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),v=(0,d.Z)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),b=(0,d.Z)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),y=(0,d.Z)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),f=(0,d.Z)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),k=(0,d.Z)("test-tube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);var j=r(7138),w=r(6393),N=r(8339);function M(){let{getActiveProviders:e,currentLanguage:t}=(0,s.l)(),[r,n]=(0,i.useState)(""),[o,l]=(0,i.useState)(!1),d="ar"===t,c=e(),u=c.some(e=>e.apiKey&&"valid"===e.validationStatus&&e.selectedModels&&e.selectedModels.length>0),p=async()=>{if(!u){n(d?"لا يوجد مقدم خدمة صالح":"No valid provider found");return}l(!0),n("");try{let e=c.find(e=>e.apiKey&&"valid"===e.validationStatus);if(!e)throw Error("No valid provider found");console.log("Testing with provider:",e);let r=await fetch("/api/llm/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({providerId:e.id,apiKey:e.apiKey,model:e.selectedModels[0]||"gpt-3.5-turbo",messages:[{role:"user",content:d?"اقترح 3 أسماء إبداعية لمشروع تطبيق جوال للتسوق الإلكتروني":"Suggest 3 creative names for a mobile e-commerce app project"}],context:{test:!0},fieldName:"test",language:t,temperature:.8,maxTokens:500})});if(console.log("Response status:",r.status),!r.ok){let e=await r.text();throw Error("API Error: ".concat(r.status," - ").concat(e))}let a=await r.json();console.log("API Result:",a),a.success?n(a.content):n("Error: ".concat(a.error))}catch(e){console.error("Test error:",e),n("Error: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{l(!1)}};return(0,a.jsxs)("div",{className:"p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-900 dark:text-white",children:d?"اختبار توليد الذكاء الاصطناعي":"AI Generation Test"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:[d?"مقدمي الخدمة النشطين:":"Active Providers:"," ",c.length]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:[d?"مقدم خدمة صالح:":"Valid Provider:"," ",u?"✅":"❌"]}),c.map(e=>{var t;return(0,a.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-1 p-2 bg-gray-50 dark:bg-gray-700 rounded",children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.id,": ",e.validationStatus]}),(0,a.jsxs)("div",{children:["API Key: ",e.apiKey?"✅":"❌"]}),(0,a.jsxs)("div",{children:["Selected Models: ",(null===(t=e.selectedModels)||void 0===t?void 0:t.length)||0]}),e.selectedModels&&e.selectedModels.length>0&&(0,a.jsxs)("div",{className:"text-xs text-blue-600 dark:text-blue-400",children:["Models: ",e.selectedModels.join(", ")]})]},e.id)})]}),(0,a.jsxs)("button",{onClick:p,disabled:o||!u,className:"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsx)("span",{className:"relative",children:o?d?"جاري الاختبار...":"Testing...":d?"اختبار التوليد":"Test Generation"})]}),r&&(0,a.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium mb-2 text-gray-900 dark:text-white",children:d?"النتيجة:":"Result:"}),(0,a.jsx)("pre",{className:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:r})]})]})]})}function P(){let{currentLanguage:e,apiSettings:t,addProvider:r,updateProvider:d,removeProvider:P,validateProvider:S,getProvider:T}=(0,s.l)(),L="ar"===e,[C,A]=(0,i.useState)({}),[E,D]=(0,i.useState)({}),[$,I]=(0,i.useState)(!1),[K,G]=(0,i.useState)(""),[O,R]=(0,i.useState)(""),[U,Z]=(0,i.useState)({}),[V,F]=(0,i.useState)({}),[B,z]=(0,i.useState)("");(0,i.useEffect)(()=>{let e=t.providers||[],r={};e.forEach(e=>{r[e.id]=e.selectedModels||[]}),F(r)},[t.providers]);let q={title:L?"إعدادات نماذج الذكاء الاصطناعي":"LLM API Settings",subtitle:L?"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة":"Configure your API keys and AI models",providers:L?"مقدمو الخدمة":"LLM Providers",addProvider:L?"إضافة مقدم خدمة":"Add Provider",apiKey:L?"مفتاح API":"API Key",baseUrl:L?"الرابط الأساسي":"Base URL",testConnection:L?"اختبار الاتصال":"Test Connection",validating:L?"جاري التحقق...":"Validating...",valid:L?"صالح":"Valid",invalid:L?"غير صالح":"Invalid",error:L?"خطأ":"Error",models:L?"النماذج المتاحة":"Available Models",selectedModels:L?"النماذج المحددة":"Selected Models",addCustomModel:L?"إضافة نموذج مخصص":"Add Custom Model",customModelName:L?"اسم النموذج المخصص":"Custom Model Name",editModels:L?"تعديل النماذج":"Edit Models",saveModels:L?"حفظ النماذج":"Save Models",noModelsSelected:L?"لم يتم تحديد أي نماذج":"No models selected",backToHome:L?"العودة للرئيسية":"Back to Home"},_=(e,t)=>{F(r=>{let a=r[e]||[],i=a.includes(t);return{...r,[e]:i?a.filter(e=>e!==t):[...a,t]}})},H=e=>{B.trim()&&(F(t=>{let r=t[e]||[];return{...t,[e]:[...r,B.trim()]}}),z(""))},J=(e,t)=>{F(r=>{let a=r[e]||[];return{...r,[e]:a.filter(e=>e!==t)}})},W=e=>{let t=V[e]||[];d(e,{selectedModels:t}),Z(t=>({...t,[e]:!1}))},X=async()=>{if(R(""),!K){R(L?"يرجى اختيار مقدم خدمة":"Please select a provider");return}if(!o(K)){R(L?"مقدم الخدمة غير موجود":"Provider not found");return}if(T(K)){R(L?"مقدم الخدمة موجود بالفعل":"Provider already exists"),I(!1),G("");return}try{r({id:K,apiKey:"",selectedModels:[],isEnabled:!1,validationStatus:"pending"}),I(!1),G(""),R("")}catch(e){console.error("Error adding provider:",e),R(L?"حدث خطأ أثناء إضافة مقدم الخدمة":"Error adding provider")}},Q=async e=>{D(t=>({...t,[e]:{status:"validating"}}));try{let t=await S(e);D(r=>({...r,[e]:{status:t?"valid":"invalid",message:t?q.valid:q.invalid,lastValidated:new Date}}))}catch(t){D(r=>({...r,[e]:{status:"error",message:t instanceof Error?t.message:q.error}}))}},Y=e=>{switch(e){case"validating":return(0,a.jsx)(l.Z,{className:"w-4 h-4 animate-spin text-blue-500"});case"valid":return(0,a.jsx)(c,{className:"w-4 h-4 text-green-500"});case"invalid":return(0,a.jsx)(u,{className:"w-4 h-4 text-red-500"});case"error":return(0,a.jsx)(p,{className:"w-4 h-4 text-orange-500"});default:return(0,a.jsx)(g,{className:"w-4 h-4 text-gray-400"})}},ee=t.providers||[],et=n.filter(e=>!ee.some(t=>t.id===e.id));return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(j.default,{href:"/",className:"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors",children:[(0,a.jsx)(m,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-arabic",children:q.backToHome})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg",children:(0,a.jsx)(x.Z,{className:"w-6 h-6 text-blue-600 dark:text-blue-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white font-arabic",children:q.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 font-arabic",children:q.subtitle})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(N.Z,{}),(0,a.jsx)(w.Z,{})]})]})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white font-arabic",children:q.providers}),(0,a.jsxs)("button",{onClick:()=>{I(!0),R(""),G("")},className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic",children:[(0,a.jsx)(h,{className:"w-4 h-4"}),q.addProvider]})]})}),(0,a.jsx)("div",{className:"p-6 space-y-4",children:0===ee.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(v,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 font-arabic",children:L?"لم يتم إعداد أي مقدم خدمة بعد":"No providers configured yet"})]}):ee.map(e=>{let t=o(e.id),r=E[e.id];return t?(0,a.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:t.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:t.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.description})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[Y((null==r?void 0:r.status)||"idle"),(0,a.jsxs)("label",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:e.isEnabled,onChange:t=>d(e.id,{isEnabled:t.target.checked}),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:L?"نشط":"Active"})]}),(0,a.jsx)("button",{onClick:()=>P(e.id),className:"p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded",children:(0,a.jsx)(b,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:q.apiKey}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{type:C[e.id]?"text":"password",value:e.apiKey,onChange:t=>d(e.id,{apiKey:t.target.value}),placeholder:t.apiKeyPlaceholder,className:"w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)("button",{onClick:()=>A(t=>({...t,[e.id]:!t[e.id]})),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:C[e.id]?(0,a.jsx)(y,{className:"w-4 h-4"}):(0,a.jsx)(f,{className:"w-4 h-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:q.baseUrl}),(0,a.jsx)("input",{type:"text",value:e.baseUrl||t.baseUrl,onChange:t=>d(e.id,{baseUrl:t.target.value}),placeholder:t.baseUrl,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[q.models,": ",t.models.length]})}),(0,a.jsxs)("button",{onClick:()=>Q(e.id),disabled:!e.apiKey||(null==r?void 0:r.status)==="validating",className:"flex items-center gap-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm",children:[(0,a.jsx)(k,{className:"w-4 h-4"}),(null==r?void 0:r.status)==="validating"?q.validating:q.testConnection]})]}),(null==r?void 0:r.message)&&(0,a.jsx)("div",{className:"mt-2 p-2 rounded text-sm ".concat("valid"===r.status?"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300":"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300"),children:r.message}),(0,a.jsxs)("div",{className:"mt-4 border-t border-gray-200 dark:border-gray-600 pt-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white font-arabic",children:q.selectedModels}),(0,a.jsx)("button",{onClick:()=>{Z(t=>({...t,[e.id]:!t[e.id]})),V[e.id]||F(t=>({...t,[e.id]:e.selectedModels||[]}))},className:"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic",children:U[e.id]?q.saveModels:q.editModels})]}),(0,a.jsx)("div",{className:"mb-3",children:0===(e.selectedModels||[]).length?(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 font-arabic",children:q.noModelsSelected}):(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(e.selectedModels||[]).map(e=>(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic",children:e},e))})}),U[e.id]&&(0,a.jsxs)("div",{className:"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic",children:q.models}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto",children:t.models.map(t=>(0,a.jsxs)("label",{className:"flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:(V[e.id]||[]).includes(t.id),onChange:()=>_(e.id,t.id),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:t.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:t.description})]})]},t.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic",children:q.addCustomModel}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"text",value:B,onChange:e=>z(e.target.value),placeholder:q.customModelName,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",onKeyDown:t=>"Enter"===t.key&&H(e.id)}),(0,a.jsx)("button",{onClick:()=>H(e.id),disabled:!B.trim(),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm",children:(0,a.jsx)(h,{className:"w-4 h-4"})})]})]}),(V[e.id]||[]).length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic",children:q.selectedModels}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:(V[e.id]||[]).map(t=>(0,a.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs",children:[(0,a.jsx)("span",{className:"font-arabic",children:t}),(0,a.jsx)("button",{onClick:()=>J(e.id,t),className:"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400",children:(0,a.jsx)(b,{className:"w-3 h-3"})})]},t))})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{onClick:()=>W(e.id),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic",children:q.saveModels})})]})]})]})]},e.id):null})})]}),(0,a.jsx)(M,{})]})}),$&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic",children:q.addProvider}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("select",{value:K,onChange:e=>G(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,a.jsx)("option",{value:"",children:L?"اختر مقدم الخدمة":"Select Provider"}),et.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.icon," ",e.name]},e.id))]})}),O&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(p,{className:"w-4 h-4 text-red-500"}),(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300 font-arabic",children:O})]})}),(0,a.jsxs)("div",{className:"flex gap-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>{I(!1),R(""),G("")},className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:L?"إلغاء":"Cancel"}),(0,a.jsx)("button",{onClick:X,disabled:!K,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:L?"إضافة":"Add"})]})]})})]})}},8339:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(7437),i=r(4792);function s(){let{currentLanguage:e,setLanguage:t}=(0,i.l)();return(0,a.jsxs)("button",{onClick:()=>t("ar"===e?"en":"ar"),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"ar"===e?"Switch to English":"التبديل إلى العربية",title:"ar"===e?"Switch to English":"التبديل إلى العربية",children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,a.jsx)("span",{className:"text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ".concat("en"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"),children:"EN"}),(0,a.jsx)("span",{className:"text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ".concat("ar"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"),children:"عر"})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}},8546:function(e,t,r){"use strict";r.d(t,{F:function(){return o},ThemeProvider:function(){return n}});var a=r(7437),i=r(2265);let s=(0,i.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,i.useState)("light"),[o,l]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(e||t),l(!0)},[]),(0,i.useEffect)(()=>{if(o){let e=document.documentElement;"dark"===r?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",r)}},[r,o]),o)?(0,a.jsx)(s.Provider,{value:{theme:r,toggleTheme:()=>{n(e=>"light"===e?"dark":"light")}},children:t}):(0,a.jsx)(a.Fragment,{children:t})}function o(){let e=(0,i.useContext)(s);return void 0===e?{theme:"light",toggleTheme:()=>{}}:e}},6393:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var a=r(7437),i=r(8546);function s(){let{theme:e,toggleTheme:t}=(0,i.F)();return(0,a.jsxs)("button",{onClick:t,className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"Switch to ".concat("light"===e?"dark":"light"," mode"),title:"Switch to ".concat("light"===e?"dark":"light"," mode"),children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[(0,a.jsx)("svg",{className:"w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ".concat("light"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),(0,a.jsx)("svg",{className:"w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ".concat("dark"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}},4792:function(e,t,r){"use strict";r.d(t,{l:function(){return n}});var a=r(903),i=r(9291);let s={projectDefinition:{name:"",purpose:"",targetUsers:"",goals:"",scope:"",timeline:""},contextMap:{timeContext:"",language:"",location:"",culturalContext:"",behavioralAspects:"",environmentalFactors:""},emotionalTone:{personality:"",communicationStyle:"",userExperience:"",brandVoice:"",emotionalIntelligence:"",interactionFlow:""},technicalLayer:{programmingLanguages:"",frameworks:"",llmModels:"",databases:"",apis:"",infrastructure:""},legalRisk:{privacyConcerns:"",dataProtection:"",compliance:"",risks:"",mitigation:"",ethicalConsiderations:""},currentLanguage:"ar",outputFormat:"markdown",apiSettings:{providers:[],globalSettings:{temperature:.7,topP:.9,maxTokens:1e3,timeout:3e4},openaiApiKey:"",openrouterApiKey:"",customModels:[]}},n=(0,a.U)()((0,i.tJ)((e,t)=>({...s,updateProjectDefinition:t=>e(e=>({projectDefinition:{...e.projectDefinition,...t}})),updateContextMap:t=>e(e=>({contextMap:{...e.contextMap,...t}})),updateEmotionalTone:t=>e(e=>({emotionalTone:{...e.emotionalTone,...t}})),updateTechnicalLayer:t=>e(e=>({technicalLayer:{...e.technicalLayer,...t}})),updateLegalRisk:t=>e(e=>({legalRisk:{...e.legalRisk,...t}})),setLanguage:t=>e({currentLanguage:t}),setOutputFormat:t=>e({outputFormat:t}),setApiSettings:t=>e({apiSettings:{...t,providers:t.providers||[]}}),addProvider:t=>e(e=>{let r=e.apiSettings.providers||[];return r.find(e=>e.id===t.id)?(console.warn("Provider with id ".concat(t.id," already exists")),e):{apiSettings:{...e.apiSettings,providers:[...r,t]}}}),updateProvider:(t,r)=>e(e=>({apiSettings:{...e.apiSettings,providers:(e.apiSettings.providers||[]).map(e=>e.id===t?{...e,...r}:e)}})),removeProvider:t=>e(e=>({apiSettings:{...e.apiSettings,providers:(e.apiSettings.providers||[]).filter(e=>e.id!==t),defaultProvider:e.apiSettings.defaultProvider===t?void 0:e.apiSettings.defaultProvider}})),validateProvider:async e=>{let r=t(),a=(r.apiSettings.providers||[]).find(t=>t.id===e);if(!a)return!1;try{r.updateProvider(e,{validationStatus:"pending",errorMessage:void 0});let t=await fetch("/api/llm/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({providerId:a.id,apiKey:a.apiKey,baseUrl:a.baseUrl})}),i=await t.json();if(i.valid)return r.updateProvider(e,{validationStatus:"valid",lastValidated:new Date,errorMessage:void 0,isEnabled:!0}),!0;return r.updateProvider(e,{validationStatus:"invalid",errorMessage:i.error||"Validation failed"}),!1}catch(t){return r.updateProvider(e,{validationStatus:"error",errorMessage:t instanceof Error?t.message:"Unknown error"}),!1}},getProvider:e=>(t().apiSettings.providers||[]).find(t=>t.id===e),getActiveProviders:()=>(t().apiSettings.providers||[]).filter(e=>e.isEnabled),setDefaultProvider:t=>e(e=>({apiSettings:{...e.apiSettings,defaultProvider:t}})),resetAll:()=>e(s),getModuleData:e=>{let r=t();switch(e){case"project":return r.projectDefinition;case"context":return r.contextMap;case"emotional":return r.emotionalTone;case"technical":return r.technicalLayer;case"legal":return r.legalRisk;default:return{}}},getAllData:()=>{let e=t();return{projectDefinition:e.projectDefinition,contextMap:e.contextMap,emotionalTone:e.emotionalTone,technicalLayer:e.technicalLayer,legalRisk:e.legalRisk}}}),{name:"contextkit-storage",version:1}))}},function(e){e.O(0,[333,971,23,744],function(){return e(e.s=3515)}),_N_E=e.O()}]);