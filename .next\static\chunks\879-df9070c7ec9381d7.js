"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{3274:function(e,t,r){r.d(t,{Z:function(){return a}});let a=(0,r(8030).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},5924:function(e,t,r){r.d(t,{Z:function(){return d}});var a=r(7437),n=r(5101),s=r(4792);function o(e){let{currentModule:t}=e,{projectDefinition:r,contextMap:n,emotionalTone:o,technicalLayer:l,legalRisk:i,currentLanguage:d}=(0,s.l)(),c="ar"===d,m=[{key:"project-definition",name:"Project Definition",nameAr:"تعريف المشروع",emoji:"\uD83C\uDFAF",data:r,href:"/project-definition"},{key:"context-map",name:"Context Map",nameAr:"خريطة السياق",emoji:"\uD83D\uDDFA️",data:n,href:"/context-map"},{key:"emotional-tone",name:"Emotional Tone",nameAr:"النبرة العاطفية",emoji:"✨",data:o,href:"/emotional-tone"},{key:"technical-layer",name:"Technical Layer",nameAr:"الطبقة التقنية",emoji:"⚙️",data:l,href:"/technical-layer"},{key:"legal-risk",name:"Legal & Privacy",nameAr:"القانونية والخصوصية",emoji:"\uD83D\uDD12",data:i,href:"/legal-risk"}],u=e=>{let t=Object.keys(e).length,r=Object.values(e).filter(e=>e&&"string"==typeof e&&e.trim()).length;return t>0?r/t*100:0},g=m.reduce((e,t)=>e+u(t.data),0)/m.length;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c?"تقدم المشروع":"Project Progress"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[Math.round(g),"% ",c?"مكتمل":"Complete"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(g,"%")}})}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:m.map(e=>{let r=u(e.data),n=t===e.key;return(0,a.jsx)("a",{href:e.href,className:"p-3 rounded-lg border-2 transition-all hover:shadow-md ".concat(n?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"),children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:e.emoji}),(0,a.jsx)("div",{className:"text-xs font-medium text-gray-900 dark:text-white mb-2",children:c?e.nameAr:e.name}),(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1",children:(0,a.jsx)("div",{className:"h-1 rounded-full transition-all duration-300 ".concat(100===r?"bg-green-500":"bg-blue-500"),style:{width:"".concat(r,"%")}})}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[Math.round(r),"%"]})]})},e.key)})}),(0,a.jsxs)("div",{className:"mt-6 flex justify-center space-x-4",children:[(0,a.jsxs)("a",{href:"/final-preview",className:"flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors",children:[(0,a.jsx)("span",{className:"mr-1",children:"\uD83D\uDCCB"}),c?"المعاينة النهائية":"Final Preview"]}),g>0&&(0,a.jsxs)("button",{onClick:()=>{confirm(c?"هل أنت متأكد من إعادة تعيين جميع البيانات؟":"Are you sure you want to reset all data?")&&window.location.reload()},className:"flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors",children:[(0,a.jsx)("span",{className:"mr-1",children:"\uD83D\uDD04"}),c?"إعادة تعيين":"Reset"]})]})]})}var l=r(2265);function i(){let[e,t]=(0,l.useState)(null),[r,n]=(0,l.useState)(!1),{currentLanguage:o}=(0,s.l)(),i="ar"===o;return((0,l.useEffect)(()=>{let e=setInterval(()=>{n(!0),setTimeout(()=>{t(new Date),n(!1)},500)},1e4);return()=>clearInterval(e)},[]),e||r)?(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-50",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:i?"جاري الحفظ...":"Saving..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[i?"تم الحفظ":"Saved"," ",null==e?void 0:e.toLocaleTimeString()]})]})})}):null}function d(e){let{title:t,titleAr:r,subtitle:l,subtitleAr:d,emoji:c,moduleKey:m,backLink:u,nextLink:g,children:h,rightPanel:x}=e,{currentLanguage:p}=(0,s.l)(),b="ar"===p;return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)(n.default,{title:b?r:t,subtitle:b?d:l,emoji:c,backLink:u?{href:u.href,label:b?u.labelAr:u.label}:void 0}),(0,a.jsx)(o,{currentModule:m}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto",children:[(0,a.jsx)("div",{className:"space-y-6 ".concat(b?"lg:order-2":"lg:order-1"),children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"✍️"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:b?"الأسئلة الذكية":"Smart Questions"})]}),h]})}),(0,a.jsx)("div",{className:"space-y-6 ".concat(b?"lg:order-1":"lg:order-2"),children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC4"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:b?"المخرجات المجمّعة":"Generated Outputs"})]})}),x]})})]}),(u||g)&&(0,a.jsxs)("div",{className:"flex justify-between items-center mt-12 max-w-7xl mx-auto",children:[u?(0,a.jsxs)("a",{href:u.href,className:"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors",children:[(0,a.jsx)("span",{className:"mr-2",children:"←"}),b?u.labelAr:u.label]}):(0,a.jsx)("div",{}),g&&(0,a.jsxs)("a",{href:g.href,className:"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[b?g.labelAr:g.label,(0,a.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,a.jsx)(i,{})]})})}},6358:function(e,t,r){r.d(t,{Z:function(){return o}});var a=r(7437),n=r(2265),s=r(4792);function o(e){let{moduleData:t,moduleName:r,moduleNameAr:o}=e,{currentLanguage:l,outputFormat:i,setOutputFormat:d}=(0,s.l)(),[c,m]=(0,n.useState)(!1),u="ar"===l,g=()=>{let e="# ".concat(u?o:r,"\n\n");return Object.entries(t).forEach(t=>{let[r,a]=t;if(a&&"string"==typeof a&&a.trim()){let t=r.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());e+="## ".concat(t,"\n").concat(a,"\n\n")}}),e},h=()=>{let e='<div class="module-output">\n  <h1>'.concat(u?o:r,"</h1>\n");return Object.entries(t).forEach(t=>{let[r,a]=t;if(a&&"string"==typeof a&&a.trim()){let t=r.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());e+="  <section>\n    <h2>".concat(t,"</h2>\n    <p>").concat(a,"</p>\n  </section>\n")}}),e+="</div>"},x=()=>JSON.stringify({module:u?o:r,data:Object.fromEntries(Object.entries(t).filter(e=>{let[t,r]=e;return r&&"string"==typeof r&&r.trim()})),metadata:{timestamp:new Date().toISOString(),language:u?"ar":"en",version:"1.0"}},null,2),p=()=>{let e=Object.fromEntries(Object.entries(t).filter(e=>{let[t,r]=e;return r&&"string"==typeof r&&r.trim()})),a="# ".concat(u?o:r,"\n");return a+="# Generated: ".concat(new Date().toISOString(),"\n\n"),Object.entries(e).forEach(e=>{let[t,r]=e,n=t.replace(/([A-Z])/g,"_$1").toLowerCase();a+="".concat(n,": |\n"),String(r||"").split("\n").forEach(e=>{a+="  ".concat(e,"\n")}),a+="\n"}),a},b=()=>{switch(i){case"markdown":default:return g();case"html":return h();case"json":return x();case"yaml":return p()}},f=async()=>{let e=b();await navigator.clipboard.writeText(e),m(!0),setTimeout(()=>m(!1),2e3)},v=Object.values(t).some(e=>e&&"string"==typeof e&&e.trim());return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex space-x-2 flex-wrap gap-2",children:["markdown","html","json","yaml"].map(e=>(0,a.jsx)("button",{onClick:()=>d(e),className:"px-3 py-1 text-sm rounded-lg transition-colors ".concat(i===e?"bg-blue-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"),children:e.toUpperCase()},e))}),v&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:f,className:"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-1",children:[(0,a.jsx)("span",{className:c?"animate-bounce":"group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCCB"}),c?u?"تم النسخ!":"Copied!":u?"نسخ الكل":"Copy All"]})]}),(0,a.jsxs)("button",{onClick:()=>{let e=b(),t="".concat(r.toLowerCase().replace(/\s+/g,"-"),".").concat({markdown:"md",html:"html",json:"json",yaml:"yml"}[i]||"txt"),a=new Blob([e],{type:{markdown:"text/markdown",html:"text/html",json:"application/json",yaml:"text/yaml"}[i]||"text/plain"}),n=URL.createObjectURL(a),s=document.createElement("a");s.href=n,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(n)},className:"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-1",children:[(0,a.jsx)("span",{className:"group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCBE"}),u?"تحميل":"Download"]})]})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]",children:v?(0,a.jsx)("pre",{className:"text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ".concat(u?"text-right":"text-left"),children:b()}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500 dark:text-gray-400",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("span",{className:"text-4xl mb-2 block",children:"\uD83D\uDCDD"}),(0,a.jsx)("p",{children:u?"ابدأ بالإجابة على الأسئلة لرؤية المخرجات":"Start answering questions to see outputs"})]})})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["markdown"===i&&(u?"تنسيق Markdown - جاهز للاستخدام في المستندات":"Markdown format - Ready for documentation"),"html"===i&&(u?"تنسيق HTML - جاهز للمواقع الإلكترونية":"HTML format - Ready for websites"),"json"===i&&(u?"تنسيق JSON - جاهز للبرمجة والـ APIs":"JSON format - Ready for programming and APIs"),"yaml"===i&&(u?"تنسيق YAML - جاهز للتكوين والنشر":"YAML format - Ready for configuration and deployment")]})]})}},5097:function(e,t,r){r.d(t,{Z:function(){return d}});var a=r(7437),n=r(2265),s=r(4792);let o=(0,r(8030).Z)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var l=r(3274);function i(e){let{fieldName:t,fieldValue:r,onValueChange:i,placeholder:d,context:c,className:m=""}=e,{currentLanguage:u,getActiveProviders:g,getAllData:h}=(0,s.l)(),[x,p]=(0,n.useState)(!1),[b,f]=(0,n.useState)([]),[v,y]=(0,n.useState)(!1),[j,w]=(0,n.useState)(null),[N,k]=(0,n.useState)([]),[D,C]=(0,n.useState)(!1),S="ar"===u;(0,n.useEffect)(()=>{C(!0),k(g())},[g]);let A=D&&N.some(e=>e.apiKey&&"valid"===e.validationStatus&&e.selectedModels&&e.selectedModels.length>0),E={generateWithAI:S?"\uD83D\uDCC4 توليد بالذكاء الاصطناعي":"\uD83D\uDCC4 Generate with AI",generating:S?"جاري التوليد...":"Generating...",noProviders:S?"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً":"Please configure an AI provider and select models in Settings first",error:S?"حدث خطأ أثناء التوليد":"Error occurred during generation"},P=async()=>{if(!A){console.warn("No valid provider available:",{activeProviders:N,hasValidProvider:A}),alert(E.noProviders);return}p(!0),f([]),y(!0);try{let e=h(),a=N.find(e=>e.apiKey&&"valid"===e.validationStatus);if(console.log("Using provider:",null==a?void 0:a.name,"with model:",null==a?void 0:a.selectedModels[0]),!a)throw Error("No valid provider found");let n=O(t,r,e,S);console.log("Generated prompt:",n);let s={providerId:a.id,apiKey:a.apiKey,model:a.selectedModels[0]||"gpt-3.5-turbo",messages:[{role:"user",content:n}],context:e,fieldName:t,language:u,temperature:.8,maxTokens:500};console.log("Sending request to API:",s);let o=await fetch("/api/llm/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(console.log("API Response status:",o.status),!o.ok){let e=await o.text();throw console.error("API Error:",e),Error("API Error: ".concat(o.status," - ").concat(e))}let l=await o.json();if(console.log("API Result:",l),l.success){let e=I(l.content);console.log("Parsed suggestions:",e),f(e),0===e.length&&f([S?"لم يتم العثور على اقتراحات مناسبة":"No suitable suggestions found"])}else throw Error(l.error||"Generation failed")}catch(t){console.error("Generation error:",t);let e=t instanceof Error?t.message:"Unknown error";f(["".concat(E.error,": ").concat(e)])}finally{p(!1)}},O=(e,t,r,a)=>{let n={name:{ar:"اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:\n- قصيرة وسهلة التذكر\n- تعكس طبيعة المشروع\n- مناسبة للجمهور المستهدف\n- أصلية ومميزة",en:"Suggest 3 creative and suitable project names. The names should be:\n- Short and memorable\n- Reflect the project nature\n- Suitable for target audience\n- Original and distinctive"},purpose:{ar:"اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:\n- واضحة ومباشرة\n- تركز على القيمة المضافة\n- تجذب المستخدمين المستهدفين\n- تميز المشروع عن المنافسين",en:"Write 3 different versions describing the project purpose. They should be:\n- Clear and direct\n- Focus on added value\n- Attract target users\n- Differentiate from competitors"},targetUsers:{ar:"حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:\n- الخصائص الديموغرافية\n- الاحتياجات والتحديات\n- السلوكيات والتفضيلات",en:"Define 3 different target user groups. For each group mention:\n- Demographic characteristics\n- Needs and challenges\n- Behaviors and preferences"},goals:{ar:"اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:\n- محددة وقابلة للقياس\n- قابلة للتحقيق وواقعية\n- مرتبطة بالجدول الزمني\n- تدعم رؤية المشروع",en:"Suggest 3 sets of goals (short, medium, long-term). They should be:\n- Specific and measurable\n- Achievable and realistic\n- Time-bound\n- Support project vision"}}[e],s=n?a?n.ar:n.en:a?"اقترح 3 خيارات مختلفة لـ ".concat(e):"Suggest 3 different options for ".concat(e),o=a?"السياق الحالي للمشروع:\n".concat(JSON.stringify(r,null,2),"\n\n"):"Current project context:\n".concat(JSON.stringify(r,null,2),"\n\n");return"".concat(o).concat(t?a?"القيمة الحالية: ".concat(t,"\n\n"):"Current value: ".concat(t,"\n\n"):"").concat(s,"\n\n").concat(a?"تعليمات:\n1. قدم 3 اقتراحات مختلفة ومتنوعة\n2. رقم كل اقتراح (1، 2، 3)\n3. اجعل كل اقتراح في سطر منفصل\n4. استخدم اللغة العربية الواضحة\n5. اعتمد على السياق المتوفر لتحسين الاقتراحات":"Instructions:\n1. Provide 3 different and diverse suggestions\n2. Number each suggestion (1, 2, 3)\n3. Put each suggestion on a separate line\n4. Use clear English\n5. Use the available context to improve suggestions")},I=e=>{let t=e.split("\n").filter(e=>e.trim()),r=[];for(let e of t)if(/^\d+[.\-\)]\s*/.test(e.trim())||/^[•\-\*]\s*/.test(e.trim())){let t=e.replace(/^\d+[.\-\)]\s*/,"").replace(/^[•\-\*]\s*/,"").trim();t&&t.length>10&&r.push(t)}else e.trim().length>20&&!e.includes(":")&&r.length<3&&r.push(e.trim());return 0===r.length?e.split(/[.!?]+/).filter(e=>e.trim().length>20).slice(0,3).map(e=>e.trim()):r.slice(0,3)};return D?(0,a.jsx)("div",{className:"relative ".concat(m),children:(0,a.jsx)("button",{onClick:P,disabled:x||!A,className:"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group ".concat(A?"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95":"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30"," ").concat(x?"animate-pulse scale-105":""),title:A?E.generateWithAI:E.noProviders,children:(0,a.jsxs)("div",{className:"relative flex items-center gap-2",children:[x?(0,a.jsx)(l.Z,{className:"w-4 h-4 animate-spin"}):(0,a.jsx)(o,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),(0,a.jsx)("span",{className:"font-arabic font-medium",children:x?E.generating:E.generateWithAI})]})})}):(0,a.jsx)("div",{className:"relative ".concat(m),children:(0,a.jsxs)("button",{disabled:!0,className:"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 cursor-not-allowed font-arabic",children:[(0,a.jsx)(o,{className:"w-4 h-4"}),S?"\uD83D\uDCC4 توليد بالذكاء الاصطناعي":"\uD83D\uDCC4 Generate with AI"]})})}function d(e){let{id:t,question:r,questionAr:o,placeholder:l,placeholderAr:d,value:c,onChange:m,type:u="textarea",aiSuggestion:g,aiSuggestionAr:h,promptTemplate:x}=e,{currentLanguage:p}=(0,s.l)(),[b,f]=(0,n.useState)(!1),[v,y]=(0,n.useState)(!1),j="ar"===p,w=async()=>{c.trim()&&(await navigator.clipboard.writeText(c),y(!0),setTimeout(()=>y(!1),2e3))},N=async()=>{if(x&&c.trim()){let e=x.replace("{answer}",c);await navigator.clipboard.writeText(e),y(!0),setTimeout(()=>y(!1),2e3)}};return(0,a.jsxs)("div",{className:"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:j?o:r}),(g||h)&&(0,a.jsxs)("button",{onClick:()=>f(!b),className:"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center",children:[(0,a.jsx)("span",{className:"mr-1",children:"\uD83E\uDDE0"}),j?"عرض الاقتراح الذكي":"Show AI Suggestion"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,a.jsx)(i,{fieldName:t,fieldValue:c,onValueChange:m,placeholder:j?d:l,className:"flex-shrink-0"}),c.trim()&&(0,a.jsxs)("button",{onClick:w,className:"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-1",children:[(0,a.jsx)("span",{className:v?"animate-bounce":"group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCCE"}),v?j?"تم النسخ!":"Copied!":j?"نسخ كـ Prompt":"Copy as Prompt"]})]})]})]}),b&&(g||h)&&(0,a.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"text-blue-500 mr-2",children:"\uD83D\uDCA1"}),(0,a.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:j?h:g})]})}),"textarea"===u?(0,a.jsx)("textarea",{value:c,onChange:e=>m(e.target.value),placeholder:j?d:l,className:"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none ".concat(j?"text-right":"text-left"),rows:4,dir:j?"rtl":"ltr"}):(0,a.jsx)("input",{type:"text",value:c,onChange:e=>m(e.target.value),placeholder:j?d:l,className:"w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white ".concat(j?"text-right":"text-left"),dir:j?"rtl":"ltr"}),x&&c.trim()&&(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{onClick:N,className:"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors",children:[(0,a.jsx)("span",{className:"mr-1",children:"\uD83D\uDE80"}),j?"نسخ كـ Prompt":"Copy as Prompt"]})})]})}}}]);