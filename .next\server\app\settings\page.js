/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/settings/page";
exports.ids = ["app/settings/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(rsc)/./src/app/settings/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/settings/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/settings/page\",\n        pathname: \"/settings\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ThemeProvider.tsx */ \"(ssr)/./src/components/ThemeProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q1RoZW1lUHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNmYWlzcyU1QyU1Q0Rlc2t0b3AlNUMlNUNDb250ZXh0S2l0JTVDJTVDc3JjJTVDJTVDc3R5bGVzJTVDJTVDZ2xhc3MtZWZmZWN0cy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdMQUFpSiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvPzc5ZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFRoZW1lUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Ccomponents%5C%5CThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Cstyles%5C%5Cglass-effects.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/settings/page.tsx */ \"(ssr)/./src/app/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2ZhaXNzJTVDJTVDRGVza3RvcCU1QyU1Q0NvbnRleHRLaXQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNzZXR0aW5ncyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBd0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb250ZXh0a2l0Lz8zZDExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcZmFpc3NcXFxcRGVza3RvcFxcXFxDb250ZXh0S2l0XFxcXHNyY1xcXFxhcHBcXFxcc2V0dGluZ3NcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cfaiss%5C%5CDesktop%5C%5CContextKit%5C%5Csrc%5C%5Capp%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* harmony import */ var _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/llmProviders */ \"(ssr)/./src/lib/llmProviders.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,CheckCircle,Clock,Database,Eye,EyeOff,Loader2,Plus,Settings,TestTube,Trash2,XCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/test-tube.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ThemeToggle */ \"(ssr)/./src/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/LanguageToggle */ \"(ssr)/./src/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/TestAIGeneration */ \"(ssr)/./src/components/TestAIGeneration.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    const { currentLanguage, apiSettings, addProvider, updateProvider, removeProvider, validateProvider, getProvider } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const isArabic = currentLanguage === \"ar\";\n    const [showKeys, setShowKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [validationStates, setValidationStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAddProvider, setShowAddProvider] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProviderId, setSelectedProviderId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [expandedProviders, setExpandedProviders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [selectedModels, setSelectedModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [newCustomModel, setNewCustomModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // تهيئة النماذج المحددة عند تحميل الصفحة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const configuredProviders = apiSettings.providers || [];\n        const initialModels = {};\n        configuredProviders.forEach((provider)=>{\n            initialModels[provider.id] = provider.selectedModels || [];\n        });\n        setSelectedModels(initialModels);\n    }, [\n        apiSettings.providers\n    ]);\n    const translations = {\n        title: isArabic ? \"إعدادات نماذج الذكاء الاصطناعي\" : \"LLM API Settings\",\n        subtitle: isArabic ? \"قم بإعداد مفاتيح API ونماذج الذكاء الاصطناعي المختلفة\" : \"Configure your API keys and AI models\",\n        providers: isArabic ? \"مقدمو الخدمة\" : \"LLM Providers\",\n        addProvider: isArabic ? \"إضافة مقدم خدمة\" : \"Add Provider\",\n        apiKey: isArabic ? \"مفتاح API\" : \"API Key\",\n        baseUrl: isArabic ? \"الرابط الأساسي\" : \"Base URL\",\n        testConnection: isArabic ? \"اختبار الاتصال\" : \"Test Connection\",\n        validating: isArabic ? \"جاري التحقق...\" : \"Validating...\",\n        valid: isArabic ? \"صالح\" : \"Valid\",\n        invalid: isArabic ? \"غير صالح\" : \"Invalid\",\n        error: isArabic ? \"خطأ\" : \"Error\",\n        models: isArabic ? \"النماذج المتاحة\" : \"Available Models\",\n        selectedModels: isArabic ? \"النماذج المحددة\" : \"Selected Models\",\n        addCustomModel: isArabic ? \"إضافة نموذج مخصص\" : \"Add Custom Model\",\n        customModelName: isArabic ? \"اسم النموذج المخصص\" : \"Custom Model Name\",\n        editModels: isArabic ? \"تعديل النماذج\" : \"Edit Models\",\n        saveModels: isArabic ? \"حفظ النماذج\" : \"Save Models\",\n        noModelsSelected: isArabic ? \"لم يتم تحديد أي نماذج\" : \"No models selected\",\n        cancel: isArabic ? \"إلغاء\" : \"Cancel\",\n        add: isArabic ? \"إضافة\" : \"Add\",\n        backToHome: isArabic ? \"العودة للرئيسية\" : \"Back to Home\"\n    };\n    // دوال إدارة النماذج\n    const toggleModelSelection = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            const isSelected = currentModels.includes(modelId);\n            return {\n                ...prev,\n                [providerId]: isSelected ? currentModels.filter((id)=>id !== modelId) : [\n                    ...currentModels,\n                    modelId\n                ]\n            };\n        });\n    };\n    const addCustomModel = (providerId)=>{\n        if (newCustomModel.trim()) {\n            setSelectedModels((prev)=>{\n                const currentModels = prev[providerId] || [];\n                return {\n                    ...prev,\n                    [providerId]: [\n                        ...currentModels,\n                        newCustomModel.trim()\n                    ]\n                };\n            });\n            setNewCustomModel(\"\");\n        }\n    };\n    const removeCustomModel = (providerId, modelId)=>{\n        setSelectedModels((prev)=>{\n            const currentModels = prev[providerId] || [];\n            return {\n                ...prev,\n                [providerId]: currentModels.filter((id)=>id !== modelId)\n            };\n        });\n    };\n    const saveProviderModels = (providerId)=>{\n        const models = selectedModels[providerId] || [];\n        updateProvider(providerId, {\n            selectedModels: models\n        });\n        setExpandedProviders((prev)=>({\n                ...prev,\n                [providerId]: false\n            }));\n    };\n    const handleAddProvider = async ()=>{\n        setErrorMessage(\"\");\n        if (!selectedProviderId) {\n            setErrorMessage(isArabic ? \"يرجى اختيار مقدم خدمة\" : \"Please select a provider\");\n            return;\n        }\n        const providerTemplate = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(selectedProviderId);\n        if (!providerTemplate) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة غير موجود\" : \"Provider not found\");\n            return;\n        }\n        const existingProvider = getProvider(selectedProviderId);\n        if (existingProvider) {\n            setErrorMessage(isArabic ? \"مقدم الخدمة موجود بالفعل\" : \"Provider already exists\");\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            return;\n        }\n        try {\n            const newProvider = {\n                id: selectedProviderId,\n                apiKey: \"\",\n                selectedModels: [],\n                isEnabled: false,\n                validationStatus: \"pending\"\n            };\n            addProvider(newProvider);\n            setShowAddProvider(false);\n            setSelectedProviderId(\"\");\n            setErrorMessage(\"\");\n        } catch (error) {\n            console.error(\"Error adding provider:\", error);\n            setErrorMessage(isArabic ? \"حدث خطأ أثناء إضافة مقدم الخدمة\" : \"Error adding provider\");\n        }\n    };\n    const handleValidateProvider = async (providerId)=>{\n        setValidationStates((prev)=>({\n                ...prev,\n                [providerId]: {\n                    status: \"validating\"\n                }\n            }));\n        try {\n            const isValid = await validateProvider(providerId);\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: isValid ? \"valid\" : \"invalid\",\n                        message: isValid ? translations.valid : translations.invalid,\n                        lastValidated: new Date()\n                    }\n                }));\n        } catch (error) {\n            setValidationStates((prev)=>({\n                    ...prev,\n                    [providerId]: {\n                        status: \"error\",\n                        message: error instanceof Error ? error.message : translations.error\n                    }\n                }));\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"validating\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 animate-spin text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 16\n                }, this);\n            case \"valid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 16\n                }, this);\n            case \"invalid\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const configuredProviders = apiSettings.providers || [];\n    const availableProviders = _lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.LLM_PROVIDERS_DATABASE.filter((p)=>!configuredProviders.some((cp)=>cp.id === p.id));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-arabic\",\n                                                children: translations.backToHome\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 bg-blue-100 dark:bg-blue-900 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-white font-arabic\",\n                                                        children: translations.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 font-arabic\",\n                                                        children: translations.subtitle\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 border-b border-gray-200 dark:border-gray-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white font-arabic\",\n                                                children: translations.providers\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setShowAddProvider(true);\n                                                    setErrorMessage(\"\");\n                                                    setSelectedProviderId(\"\");\n                                                },\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-arabic\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    translations.addProvider\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6 space-y-4\",\n                                    children: configuredProviders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 font-arabic\",\n                                                children: isArabic ? \"لم يتم إعداد أي مقدم خدمة بعد\" : \"No providers configured yet\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 17\n                                    }, this) : configuredProviders.map((provider)=>{\n                                        const providerInfo = (0,_lib_llmProviders__WEBPACK_IMPORTED_MODULE_3__.getProviderById)(provider.id);\n                                        const validationState = validationStates[provider.id];\n                                        if (!providerInfo) return null;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border border-gray-200 dark:border-gray-600 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-2xl\",\n                                                                    children: providerInfo.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                            children: providerInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                            children: providerInfo.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                getStatusIcon(validationState?.status || \"idle\"),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: provider.isEnabled,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    isEnabled: e.target.checked\n                                                                                }),\n                                                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                            children: isArabic ? \"نشط\" : \"Active\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeProvider(provider.id),\n                                                                    className: \"p-1 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                                            children: translations.apiKey\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: showKeys[provider.id] ? \"text\" : \"password\",\n                                                                                    value: provider.apiKey,\n                                                                                    onChange: (e)=>updateProvider(provider.id, {\n                                                                                            apiKey: e.target.value\n                                                                                        }),\n                                                                                    placeholder: providerInfo.apiKeyPlaceholder,\n                                                                                    className: \"w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 346,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                    onClick: ()=>setShowKeys((prev)=>({\n                                                                                                ...prev,\n                                                                                                [provider.id]: !prev[provider.id]\n                                                                                            })),\n                                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                                                                                    children: showKeys[provider.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 357,\n                                                                                        columnNumber: 58\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                        lineNumber: 357,\n                                                                                        columnNumber: 91\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                                            children: translations.baseUrl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: provider.baseUrl || providerInfo.baseUrl,\n                                                                            onChange: (e)=>updateProvider(provider.id, {\n                                                                                    baseUrl: e.target.value\n                                                                                }),\n                                                                            placeholder: providerInfo.baseUrl,\n                                                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                        children: [\n                                                                            translations.models,\n                                                                            \": \",\n                                                                            providerInfo.models.length\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleValidateProvider(provider.id),\n                                                                    disabled: !provider.apiKey || validationState?.status === \"validating\",\n                                                                    className: \"flex items-center gap-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 388,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        validationState?.status === \"validating\" ? translations.validating : translations.testConnection\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        validationState?.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `mt-2 p-2 rounded text-sm ${validationState.status === \"valid\" ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\" : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"}`,\n                                                            children: validationState.message\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-4 border-t border-gray-200 dark:border-gray-600 pt-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white font-arabic\",\n                                                                            children: translations.selectedModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setExpandedProviders((prev)=>({\n                                                                                        ...prev,\n                                                                                        [provider.id]: !prev[provider.id]\n                                                                                    }));\n                                                                                if (!selectedModels[provider.id]) {\n                                                                                    setSelectedModels((prev)=>({\n                                                                                            ...prev,\n                                                                                            [provider.id]: provider.selectedModels || []\n                                                                                        }));\n                                                                                }\n                                                                            },\n                                                                            className: \"text-sm text-blue-600 dark:text-blue-400 hover:underline font-arabic\",\n                                                                            children: expandedProviders[provider.id] ? translations.saveModels : translations.editModels\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: (provider.selectedModels || []).length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500 dark:text-gray-400 font-arabic\",\n                                                                        children: translations.noModelsSelected\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-2\",\n                                                                        children: (provider.selectedModels || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs font-arabic\",\n                                                                                children: modelId\n                                                                            }, modelId, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 35\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                expandedProviders[provider.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.models\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto\",\n                                                                                    children: providerInfo.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"flex items-center space-x-2 p-2 hover:bg-gray-100 dark:hover:bg-gray-600 rounded cursor-pointer\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                    type: \"checkbox\",\n                                                                                                    checked: (selectedModels[provider.id] || []).includes(model.id),\n                                                                                                    onChange: ()=>toggleModelSelection(provider.id, model.id),\n                                                                                                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 456,\n                                                                                                    columnNumber: 39\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                    className: \"flex-1 min-w-0\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                                                                            children: model.name\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 463,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                                                                            children: model.description\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                            lineNumber: 466,\n                                                                                                            columnNumber: 41\n                                                                                                        }, this)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 462,\n                                                                                                    columnNumber: 39\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, model.id, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 452,\n                                                                                            columnNumber: 37\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.addCustomModel\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 477,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"text\",\n                                                                                            value: newCustomModel,\n                                                                                            onChange: (e)=>setNewCustomModel(e.target.value),\n                                                                                            placeholder: translations.customModelName,\n                                                                                            className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\",\n                                                                                            onKeyDown: (e)=>e.key === \"Enter\" && addCustomModel(provider.id)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 481,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            onClick: ()=>addCustomModel(provider.id),\n                                                                                            disabled: !newCustomModel.trim(),\n                                                                                            className: \"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                lineNumber: 494,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 489,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 480,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        (selectedModels[provider.id] || []).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white mb-2 font-arabic\",\n                                                                                    children: translations.selectedModels\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 502,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex flex-wrap gap-2\",\n                                                                                    children: (selectedModels[provider.id] || []).map((modelId)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded text-xs\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"font-arabic\",\n                                                                                                    children: modelId\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 511,\n                                                                                                    columnNumber: 41\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                                    onClick: ()=>removeCustomModel(provider.id, modelId),\n                                                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-red-600 dark:hover:text-red-400\",\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                                        className: \"w-3 h-3\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                        lineNumber: 516,\n                                                                                                        columnNumber: 43\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                                    lineNumber: 512,\n                                                                                                    columnNumber: 41\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, modelId, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                            lineNumber: 507,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                    lineNumber: 505,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex justify-end\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>saveProviderModels(provider.id),\n                                                                                className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-arabic\",\n                                                                                children: translations.saveModels\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TestAIGeneration__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            showAddProvider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4 font-arabic\",\n                            children: translations.addProvider\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedProviderId,\n                                onChange: (e)=>setSelectedProviderId(e.target.value),\n                                className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: isArabic ? \"اختر مقدم الخدمة\" : \"Select Provider\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this),\n                                    availableProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: provider.id,\n                                            children: [\n                                                provider.icon,\n                                                \" \",\n                                                provider.name\n                                            ]\n                                        }, provider.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 19\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 558,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 13\n                        }, this),\n                        errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_CheckCircle_Clock_Database_Eye_EyeOff_Loader2_Plus_Settings_TestTube_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-red-700 dark:text-red-300 font-arabic\",\n                                        children: errorMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setShowAddProvider(false);\n                                        setErrorMessage(\"\");\n                                        setSelectedProviderId(\"\");\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: isArabic ? \"إلغاء\" : \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddProvider,\n                                    disabled: !selectedProviderId,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: isArabic ? \"إضافة\" : \"Add\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 551,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/LanguageToggle.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { currentLanguage, setLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_1__.useContextStore)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setLanguage(currentLanguage === \"ar\" ? \"en\" : \"ar\"),\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        title: currentLanguage === \"ar\" ? \"Switch to English\" : \"التبديل إلى العربية\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${currentLanguage === \"en\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${currentLanguage === \"ar\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        children: \"عر\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TestAIGeneration.tsx":
/*!*********************************************!*\
  !*** ./src/components/TestAIGeneration.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAIGeneration)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_contextStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/contextStore */ \"(ssr)/./src/store/contextStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TestAIGeneration() {\n    const { getActiveProviders, currentLanguage } = (0,_store_contextStore__WEBPACK_IMPORTED_MODULE_2__.useContextStore)();\n    const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isArabic = currentLanguage === \"ar\";\n    const activeProviders = getActiveProviders();\n    const hasValidProvider = activeProviders.some((p)=>p.apiKey && p.validationStatus === \"valid\" && p.selectedModels && p.selectedModels.length > 0);\n    const testAIGeneration = async ()=>{\n        if (!hasValidProvider) {\n            setTestResult(isArabic ? \"لا يوجد مقدم خدمة صالح\" : \"No valid provider found\");\n            return;\n        }\n        setIsLoading(true);\n        setTestResult(\"\");\n        try {\n            const provider = activeProviders.find((p)=>p.apiKey && p.validationStatus === \"valid\");\n            if (!provider) {\n                throw new Error(\"No valid provider found\");\n            }\n            console.log(\"Testing with provider:\", provider);\n            const response = await fetch(\"/api/llm/generate\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    providerId: provider.id,\n                    apiKey: provider.apiKey,\n                    model: provider.selectedModels[0] || \"gpt-3.5-turbo\",\n                    messages: [\n                        {\n                            role: \"user\",\n                            content: isArabic ? \"اقترح 3 أسماء إبداعية لمشروع تطبيق جوال للتسوق الإلكتروني\" : \"Suggest 3 creative names for a mobile e-commerce app project\"\n                        }\n                    ],\n                    context: {\n                        test: true\n                    },\n                    fieldName: \"test\",\n                    language: currentLanguage,\n                    temperature: 0.8,\n                    maxTokens: 500\n                })\n            });\n            console.log(\"Response status:\", response.status);\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`API Error: ${response.status} - ${errorText}`);\n            }\n            const result = await response.json();\n            console.log(\"API Result:\", result);\n            if (result.success) {\n                setTestResult(result.content);\n            } else {\n                setTestResult(`Error: ${result.error}`);\n            }\n        } catch (error) {\n            console.error(\"Test error:\", error);\n            setTestResult(`Error: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-lg font-semibold mb-4 text-gray-900 dark:text-white\",\n                children: isArabic ? \"اختبار توليد الذكاء الاصطناعي\" : \"AI Generation Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                children: [\n                                    isArabic ? \"مقدمي الخدمة النشطين:\" : \"Active Providers:\",\n                                    \" \",\n                                    activeProviders.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                children: [\n                                    isArabic ? \"مقدم خدمة صالح:\" : \"Valid Provider:\",\n                                    \" \",\n                                    hasValidProvider ? \"✅\" : \"❌\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this),\n                            activeProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-400 mb-1 p-2 bg-gray-50 dark:bg-gray-700 rounded\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                provider.id,\n                                                \": \",\n                                                provider.validationStatus\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"API Key: \",\n                                                provider.apiKey ? \"✅\" : \"❌\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                \"Selected Models: \",\n                                                provider.selectedModels?.length || 0\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        provider.selectedModels && provider.selectedModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                            children: [\n                                                \"Models: \",\n                                                provider.selectedModels.join(\", \")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, provider.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: testAIGeneration,\n                        disabled: isLoading || !hasValidProvider,\n                        className: \"relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative\",\n                                children: isLoading ? isArabic ? \"جاري الاختبار...\" : \"Testing...\" : isArabic ? \"اختبار التوليد\" : \"Test Generation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium mb-2 text-gray-900 dark:text-white\",\n                                children: isArabic ? \"النتيجة:\" : \"Result:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap\",\n                                children: testResult\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\TestAIGeneration.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TestAIGeneration.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme from localStorage or system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedTheme = localStorage.getItem(\"theme\");\n        const systemTheme = window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n        const initialTheme = savedTheme || systemTheme;\n        setTheme(initialTheme);\n        setMounted(true);\n    }, []);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (mounted) {\n            const root = document.documentElement;\n            if (theme === \"dark\") {\n                root.classList.add(\"dark\");\n            } else {\n                root.classList.remove(\"dark\");\n            }\n            localStorage.setItem(\"theme\", theme);\n        }\n    }, [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === \"light\" ? \"dark\" : \"light\");\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeProvider.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        // Return default values instead of throwing error during SSR\n        return {\n            theme: \"light\",\n            toggleTheme: ()=>{}\n        };\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXVFO0FBU3ZFLE1BQU1JLDZCQUFlSixvREFBYUEsQ0FBK0JLO0FBRTFELFNBQVNDLGNBQWMsRUFBRUMsUUFBUSxFQUFpQztJQUN2RSxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR04sK0NBQVFBLENBQVE7SUFDMUMsTUFBTSxDQUFDTyxTQUFTQyxXQUFXLEdBQUdSLCtDQUFRQSxDQUFDO0lBRXZDLDBEQUEwRDtJQUMxREQsZ0RBQVNBLENBQUM7UUFDUixNQUFNVSxhQUFhQyxhQUFhQyxPQUFPLENBQUM7UUFDeEMsTUFBTUMsY0FBY0MsT0FBT0MsVUFBVSxDQUFDLGdDQUFnQ0MsT0FBTyxHQUFHLFNBQVM7UUFDekYsTUFBTUMsZUFBZVAsY0FBY0c7UUFFbkNOLFNBQVNVO1FBQ1RSLFdBQVc7SUFDYixHQUFHLEVBQUU7SUFFTCwwQkFBMEI7SUFDMUJULGdEQUFTQSxDQUFDO1FBQ1IsSUFBSVEsU0FBUztZQUNYLE1BQU1VLE9BQU9DLFNBQVNDLGVBQWU7WUFDckMsSUFBSWQsVUFBVSxRQUFRO2dCQUNwQlksS0FBS0csU0FBUyxDQUFDQyxHQUFHLENBQUM7WUFDckIsT0FBTztnQkFDTEosS0FBS0csU0FBUyxDQUFDRSxNQUFNLENBQUM7WUFDeEI7WUFDQVosYUFBYWEsT0FBTyxDQUFDLFNBQVNsQjtRQUNoQztJQUNGLEdBQUc7UUFBQ0E7UUFBT0U7S0FBUTtJQUVuQixNQUFNaUIsY0FBYztRQUNsQmxCLFNBQVNtQixDQUFBQSxPQUFRQSxTQUFTLFVBQVUsU0FBUztJQUMvQztJQUVBLDZCQUE2QjtJQUM3QixJQUFJLENBQUNsQixTQUFTO1FBQ1oscUJBQU87c0JBQUdIOztJQUNaO0lBRUEscUJBQ0UsOERBQUNILGFBQWF5QixRQUFRO1FBQUNDLE9BQU87WUFBRXRCO1lBQU9tQjtRQUFZO2tCQUNoRHBCOzs7Ozs7QUFHUDtBQUVPLFNBQVN3QjtJQUNkLE1BQU1DLFVBQVUvQixpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSTRCLFlBQVkzQixXQUFXO1FBQ3pCLDZEQUE2RDtRQUM3RCxPQUFPO1lBQ0xHLE9BQU87WUFDUG1CLGFBQWEsS0FBTztRQUN0QjtJQUNGO0lBQ0EsT0FBT0s7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL2NvbnRleHRraXQvLi9zcmMvY29tcG9uZW50cy9UaGVtZVByb3ZpZGVyLnRzeD82ZjQ3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxudHlwZSBUaGVtZSA9ICdsaWdodCcgfCAnZGFyayc7XG5cbmludGVyZmFjZSBUaGVtZUNvbnRleHRUeXBlIHtcbiAgdGhlbWU6IFRoZW1lO1xuICB0b2dnbGVUaGVtZTogKCkgPT4gdm9pZDtcbn1cblxuY29uc3QgVGhlbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUaGVtZUNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFt0aGVtZSwgc2V0VGhlbWVdID0gdXNlU3RhdGU8VGhlbWU+KCdsaWdodCcpO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZSBvciBzeXN0ZW0gcHJlZmVyZW5jZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNhdmVkVGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKSBhcyBUaGVtZTtcbiAgICBjb25zdCBzeXN0ZW1UaGVtZSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlcyA/ICdkYXJrJyA6ICdsaWdodCc7XG4gICAgY29uc3QgaW5pdGlhbFRoZW1lID0gc2F2ZWRUaGVtZSB8fCBzeXN0ZW1UaGVtZTtcbiAgICBcbiAgICBzZXRUaGVtZShpbml0aWFsVGhlbWUpO1xuICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gIH0sIFtdKTtcblxuICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChtb3VudGVkKSB7XG4gICAgICBjb25zdCByb290ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50O1xuICAgICAgaWYgKHRoZW1lID09PSAnZGFyaycpIHtcbiAgICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKCdkYXJrJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByb290LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKTtcbiAgICAgIH1cbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd0aGVtZScsIHRoZW1lKTtcbiAgICB9XG4gIH0sIFt0aGVtZSwgbW91bnRlZF0pO1xuXG4gIGNvbnN0IHRvZ2dsZVRoZW1lID0gKCkgPT4ge1xuICAgIHNldFRoZW1lKHByZXYgPT4gcHJldiA9PT0gJ2xpZ2h0JyA/ICdkYXJrJyA6ICdsaWdodCcpO1xuICB9O1xuXG4gIC8vIFByZXZlbnQgaHlkcmF0aW9uIG1pc21hdGNoXG4gIGlmICghbW91bnRlZCkge1xuICAgIHJldHVybiA8PntjaGlsZHJlbn08Lz47XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxUaGVtZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgdGhlbWUsIHRvZ2dsZVRoZW1lIH19PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvVGhlbWVDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVGhlbWUoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRoZW1lQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICAvLyBSZXR1cm4gZGVmYXVsdCB2YWx1ZXMgaW5zdGVhZCBvZiB0aHJvd2luZyBlcnJvciBkdXJpbmcgU1NSXG4gICAgcmV0dXJuIHtcbiAgICAgIHRoZW1lOiAnbGlnaHQnIGFzIFRoZW1lLFxuICAgICAgdG9nZ2xlVGhlbWU6ICgpID0+IHt9XG4gICAgfTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwiVGhlbWVDb250ZXh0IiwidW5kZWZpbmVkIiwiVGhlbWVQcm92aWRlciIsImNoaWxkcmVuIiwidGhlbWUiLCJzZXRUaGVtZSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwic2F2ZWRUaGVtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJzeXN0ZW1UaGVtZSIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJtYXRjaGVzIiwiaW5pdGlhbFRoZW1lIiwicm9vdCIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xhc3NMaXN0IiwiYWRkIiwicmVtb3ZlIiwic2V0SXRlbSIsInRvZ2dsZVRoZW1lIiwicHJldiIsIlByb3ZpZGVyIiwidmFsdWUiLCJ1c2VUaGVtZSIsImNvbnRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggle.tsx":
/*!****************************************!*\
  !*** ./src/components/ThemeToggle.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ThemeProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ThemeProvider */ \"(ssr)/./src/components/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_ThemeProvider__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden\",\n        \"aria-label\": `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        title: `Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${theme === \"light\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${theme === \"dark\" ? \"opacity-100 rotate-0 scale-100\" : \"opacity-0 -rotate-180 scale-0\"}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/llmProviders.ts":
/*!*********************************!*\
  !*** ./src/lib/llmProviders.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LLM_PROVIDERS_DATABASE: () => (/* binding */ LLM_PROVIDERS_DATABASE),\n/* harmony export */   getActiveProviders: () => (/* binding */ getActiveProviders),\n/* harmony export */   getModelById: () => (/* binding */ getModelById),\n/* harmony export */   getProviderBaseUrl: () => (/* binding */ getProviderBaseUrl),\n/* harmony export */   getProviderById: () => (/* binding */ getProviderById),\n/* harmony export */   getProviderHeaders: () => (/* binding */ getProviderHeaders),\n/* harmony export */   searchProviders: () => (/* binding */ searchProviders)\n/* harmony export */ });\n/**\n * قاعدة بيانات شاملة لمقدمي خدمات LLM\n * تحتوي على معلومات كاملة عن كل مقدم خدمة مع Base URLs والنماذج\n */ const LLM_PROVIDERS_DATABASE = [\n    {\n        id: \"openai\",\n        name: \"OpenAI\",\n        icon: \"\\uD83E\\uDD16\",\n        description: \"GPT models from OpenAI - Industry leading language models\",\n        baseUrl: \"https://api.openai.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 128000,\n        models: [\n            {\n                id: \"gpt-4o\",\n                name: \"GPT-4o\",\n                description: \"Most advanced multimodal model\",\n                contextLength: 128000,\n                pricing: \"$5/1M input, $15/1M output\",\n                inputPrice: 5,\n                outputPrice: 15\n            },\n            {\n                id: \"gpt-4o-mini\",\n                name: \"GPT-4o Mini\",\n                description: \"Faster and more affordable\",\n                contextLength: 128000,\n                pricing: \"$0.15/1M input, $0.6/1M output\",\n                inputPrice: 0.15,\n                outputPrice: 0.6\n            },\n            {\n                id: \"gpt-4-turbo\",\n                name: \"GPT-4 Turbo\",\n                description: \"High performance model\",\n                contextLength: 128000,\n                pricing: \"$10/1M input, $30/1M output\",\n                inputPrice: 10,\n                outputPrice: 30\n            },\n            {\n                id: \"gpt-3.5-turbo\",\n                name: \"GPT-3.5 Turbo\",\n                description: \"Fast and efficient\",\n                contextLength: 16385,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            }\n        ]\n    },\n    {\n        id: \"anthropic\",\n        name: \"Anthropic\",\n        icon: \"\\uD83E\\uDDE0\",\n        description: \"Claude models from Anthropic - Advanced reasoning capabilities\",\n        baseUrl: \"https://api.anthropic.com/v1\",\n        apiKeyPlaceholder: \"sk-ant-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"anthropic-version\": \"2023-06-01\"\n        },\n        models: [\n            {\n                id: \"claude-3-5-sonnet-20241022\",\n                name: \"Claude 3.5 Sonnet\",\n                description: \"Most intelligent model\",\n                contextLength: 200000,\n                pricing: \"$3/1M input, $15/1M output\",\n                inputPrice: 3,\n                outputPrice: 15\n            },\n            {\n                id: \"claude-3-5-haiku-20241022\",\n                name: \"Claude 3.5 Haiku\",\n                description: \"Fastest model\",\n                contextLength: 200000,\n                pricing: \"$0.25/1M input, $1.25/1M output\",\n                inputPrice: 0.25,\n                outputPrice: 1.25\n            },\n            {\n                id: \"claude-3-opus-20240229\",\n                name: \"Claude 3 Opus\",\n                description: \"Most powerful model\",\n                contextLength: 200000,\n                pricing: \"$15/1M input, $75/1M output\",\n                inputPrice: 15,\n                outputPrice: 75\n            }\n        ]\n    },\n    {\n        id: \"google\",\n        name: \"Google AI\",\n        icon: \"\\uD83D\\uDD0D\",\n        description: \"Gemini models from Google - Multimodal AI capabilities\",\n        baseUrl: \"https://generativelanguage.googleapis.com/v1beta\",\n        apiKeyPlaceholder: \"AIza...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 2000000,\n        models: [\n            {\n                id: \"gemini-1.5-pro\",\n                name: \"Gemini 1.5 Pro\",\n                description: \"Advanced reasoning with 2M context\",\n                contextLength: 2000000,\n                pricing: \"$1.25/1M input, $5/1M output\",\n                inputPrice: 1.25,\n                outputPrice: 5\n            },\n            {\n                id: \"gemini-1.5-flash\",\n                name: \"Gemini 1.5 Flash\",\n                description: \"Fast and efficient\",\n                contextLength: 1000000,\n                pricing: \"$0.075/1M input, $0.3/1M output\",\n                inputPrice: 0.075,\n                outputPrice: 0.3\n            },\n            {\n                id: \"gemini-pro\",\n                name: \"Gemini Pro\",\n                description: \"Balanced performance\",\n                contextLength: 32768,\n                pricing: \"$0.5/1M input, $1.5/1M output\",\n                inputPrice: 0.5,\n                outputPrice: 1.5\n            }\n        ]\n    },\n    {\n        id: \"openrouter\",\n        name: \"OpenRouter\",\n        icon: \"\\uD83D\\uDD00\",\n        description: \"Access to multiple models via OpenRouter - One API for all models\",\n        baseUrl: \"https://openrouter.ai/api/v1\",\n        apiKeyPlaceholder: \"sk-or-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 200000,\n        headers: {\n            \"HTTP-Referer\": process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\",\n            \"X-Title\": \"ContextKit\"\n        },\n        models: [\n            {\n                id: \"openai/gpt-4o\",\n                name: \"GPT-4o (via OpenRouter)\",\n                description: \"OpenAI GPT-4o through OpenRouter\",\n                contextLength: 128000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"anthropic/claude-3.5-sonnet\",\n                name: \"Claude 3.5 Sonnet (via OpenRouter)\",\n                description: \"Anthropic Claude through OpenRouter\",\n                contextLength: 200000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"google/gemini-pro-1.5\",\n                name: \"Gemini Pro 1.5 (via OpenRouter)\",\n                description: \"Google Gemini through OpenRouter\",\n                contextLength: 1000000,\n                pricing: \"Variable pricing\"\n            },\n            {\n                id: \"meta-llama/llama-3.1-405b-instruct\",\n                name: \"Llama 3.1 405B (via OpenRouter)\",\n                description: \"Meta Llama through OpenRouter\",\n                contextLength: 131072,\n                pricing: \"Variable pricing\"\n            }\n        ]\n    },\n    {\n        id: \"deepseek\",\n        name: \"DeepSeek\",\n        icon: \"\\uD83C\\uDF0A\",\n        description: \"DeepSeek models - Efficient and cost-effective AI\",\n        baseUrl: \"https://api.deepseek.com/v1\",\n        apiKeyPlaceholder: \"sk-...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"deepseek-chat\",\n                name: \"DeepSeek Chat\",\n                description: \"General purpose conversational AI\",\n                contextLength: 32768,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            },\n            {\n                id: \"deepseek-coder\",\n                name: \"DeepSeek Coder\",\n                description: \"Specialized for code generation\",\n                contextLength: 16384,\n                pricing: \"$0.14/1M input, $0.28/1M output\",\n                inputPrice: 0.14,\n                outputPrice: 0.28\n            }\n        ]\n    },\n    {\n        id: \"groq\",\n        name: \"Groq\",\n        icon: \"⚡\",\n        description: \"Groq - Ultra-fast inference with GroqChip technology\",\n        baseUrl: \"https://api.groq.com/openai/v1\",\n        apiKeyPlaceholder: \"gsk_...\",\n        isActive: true,\n        supportsStreaming: true,\n        maxTokens: 32768,\n        models: [\n            {\n                id: \"llama-3.1-70b-versatile\",\n                name: \"Llama 3.1 70B\",\n                description: \"Meta Llama 3.1 70B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.59/1M input, $0.79/1M output\",\n                inputPrice: 0.59,\n                outputPrice: 0.79\n            },\n            {\n                id: \"llama-3.1-8b-instant\",\n                name: \"Llama 3.1 8B\",\n                description: \"Meta Llama 3.1 8B on Groq\",\n                contextLength: 131072,\n                pricing: \"$0.05/1M input, $0.08/1M output\",\n                inputPrice: 0.05,\n                outputPrice: 0.08\n            },\n            {\n                id: \"mixtral-8x7b-32768\",\n                name: \"Mixtral 8x7B\",\n                description: \"Mistral Mixtral 8x7B on Groq\",\n                contextLength: 32768,\n                pricing: \"$0.24/1M input, $0.24/1M output\",\n                inputPrice: 0.24,\n                outputPrice: 0.24\n            }\n        ]\n    }\n];\n/**\n * الحصول على مقدم خدمة بواسطة ID\n */ function getProviderById(id) {\n    return LLM_PROVIDERS_DATABASE.find((provider)=>provider.id === id);\n}\n/**\n * الحصول على جميع مقدمي الخدمة النشطين\n */ function getActiveProviders() {\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.isActive);\n}\n/**\n * الحصول على نموذج بواسطة provider ID و model ID\n */ function getModelById(providerId, modelId) {\n    const provider = getProviderById(providerId);\n    return provider?.models.find((model)=>model.id === modelId);\n}\n/**\n * البحث عن مقدمي الخدمة\n */ function searchProviders(query) {\n    const lowercaseQuery = query.toLowerCase();\n    return LLM_PROVIDERS_DATABASE.filter((provider)=>provider.name.toLowerCase().includes(lowercaseQuery) || provider.description.toLowerCase().includes(lowercaseQuery) || provider.models.some((model)=>model.name.toLowerCase().includes(lowercaseQuery) || model.description.toLowerCase().includes(lowercaseQuery)));\n}\n/**\n * تحديد Base URL التلقائي لمقدم الخدمة\n */ function getProviderBaseUrl(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.baseUrl || \"\";\n}\n/**\n * الحصول على Headers المطلوبة لمقدم الخدمة\n */ function getProviderHeaders(providerId) {\n    const provider = getProviderById(providerId);\n    return provider?.headers || {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/llmProviders.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/contextStore.ts":
/*!***********************************!*\
  !*** ./src/store/contextStore.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useContextStore: () => (/* binding */ useContextStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\n// القيم الافتراضية\nconst initialState = {\n    projectDefinition: {\n        name: \"\",\n        purpose: \"\",\n        targetUsers: \"\",\n        goals: \"\",\n        scope: \"\",\n        timeline: \"\"\n    },\n    contextMap: {\n        timeContext: \"\",\n        language: \"\",\n        location: \"\",\n        culturalContext: \"\",\n        behavioralAspects: \"\",\n        environmentalFactors: \"\"\n    },\n    emotionalTone: {\n        personality: \"\",\n        communicationStyle: \"\",\n        userExperience: \"\",\n        brandVoice: \"\",\n        emotionalIntelligence: \"\",\n        interactionFlow: \"\"\n    },\n    technicalLayer: {\n        programmingLanguages: \"\",\n        frameworks: \"\",\n        llmModels: \"\",\n        databases: \"\",\n        apis: \"\",\n        infrastructure: \"\"\n    },\n    legalRisk: {\n        privacyConcerns: \"\",\n        dataProtection: \"\",\n        compliance: \"\",\n        risks: \"\",\n        mitigation: \"\",\n        ethicalConsiderations: \"\"\n    },\n    currentLanguage: \"ar\",\n    outputFormat: \"markdown\",\n    apiSettings: {\n        providers: [],\n        globalSettings: {\n            temperature: 0.7,\n            topP: 0.9,\n            maxTokens: 1000,\n            timeout: 30000\n        },\n        // Legacy support\n        openaiApiKey: \"\",\n        openrouterApiKey: \"\",\n        customModels: []\n    }\n};\n// إنشاء المتجر مع التخزين المستمر\nconst useContextStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        ...initialState,\n        updateProjectDefinition: (data)=>set((state)=>({\n                    projectDefinition: {\n                        ...state.projectDefinition,\n                        ...data\n                    }\n                })),\n        updateContextMap: (data)=>set((state)=>({\n                    contextMap: {\n                        ...state.contextMap,\n                        ...data\n                    }\n                })),\n        updateEmotionalTone: (data)=>set((state)=>({\n                    emotionalTone: {\n                        ...state.emotionalTone,\n                        ...data\n                    }\n                })),\n        updateTechnicalLayer: (data)=>set((state)=>({\n                    technicalLayer: {\n                        ...state.technicalLayer,\n                        ...data\n                    }\n                })),\n        updateLegalRisk: (data)=>set((state)=>({\n                    legalRisk: {\n                        ...state.legalRisk,\n                        ...data\n                    }\n                })),\n        setLanguage: (lang)=>set({\n                currentLanguage: lang\n            }),\n        setOutputFormat: (format)=>set({\n                outputFormat: format\n            }),\n        setApiSettings: (settings)=>set({\n                apiSettings: {\n                    ...settings,\n                    providers: settings.providers || []\n                }\n            }),\n        // LLM Provider management functions\n        addProvider: (provider)=>set((state)=>{\n                const existingProviders = state.apiSettings.providers || [];\n                // تحقق من عدم وجود مقدم خدمة بنفس المعرف\n                if (existingProviders.find((p)=>p.id === provider.id)) {\n                    console.warn(`Provider with id ${provider.id} already exists`);\n                    return state; // لا تضيف مقدم خدمة مكرر\n                }\n                return {\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: [\n                            ...existingProviders,\n                            provider\n                        ]\n                    }\n                };\n            }),\n        updateProvider: (providerId, updates)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).map((p)=>p.id === providerId ? {\n                                ...p,\n                                ...updates\n                            } : p)\n                    }\n                })),\n        removeProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        providers: (state.apiSettings.providers || []).filter((p)=>p.id !== providerId),\n                        defaultProvider: state.apiSettings.defaultProvider === providerId ? undefined : state.apiSettings.defaultProvider\n                    }\n                })),\n        validateProvider: async (providerId)=>{\n            const state = get();\n            const provider = (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n            if (!provider) return false;\n            try {\n                // Update status to pending\n                state.updateProvider(providerId, {\n                    validationStatus: \"pending\",\n                    errorMessage: undefined\n                });\n                // Call validation API\n                const response = await fetch(\"/api/llm/validate\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        providerId: provider.id,\n                        apiKey: provider.apiKey,\n                        baseUrl: provider.baseUrl\n                    })\n                });\n                const result = await response.json();\n                if (result.valid) {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"valid\",\n                        lastValidated: new Date(),\n                        errorMessage: undefined,\n                        isEnabled: true // تفعيل المقدم تلقائياً عند نجاح التحقق\n                    });\n                    return true;\n                } else {\n                    state.updateProvider(providerId, {\n                        validationStatus: \"invalid\",\n                        errorMessage: result.error || \"Validation failed\"\n                    });\n                    return false;\n                }\n            } catch (error) {\n                state.updateProvider(providerId, {\n                    validationStatus: \"error\",\n                    errorMessage: error instanceof Error ? error.message : \"Unknown error\"\n                });\n                return false;\n            }\n        },\n        getProvider: (providerId)=>{\n            const state = get();\n            return (state.apiSettings.providers || []).find((p)=>p.id === providerId);\n        },\n        getActiveProviders: ()=>{\n            const state = get();\n            return (state.apiSettings.providers || []).filter((p)=>p.isEnabled);\n        },\n        setDefaultProvider: (providerId)=>set((state)=>({\n                    apiSettings: {\n                        ...state.apiSettings,\n                        defaultProvider: providerId\n                    }\n                })),\n        resetAll: ()=>set(initialState),\n        getModuleData: (module)=>{\n            const state = get();\n            switch(module){\n                case \"project\":\n                    return state.projectDefinition;\n                case \"context\":\n                    return state.contextMap;\n                case \"emotional\":\n                    return state.emotionalTone;\n                case \"technical\":\n                    return state.technicalLayer;\n                case \"legal\":\n                    return state.legalRisk;\n                default:\n                    return {};\n            }\n        },\n        getAllData: ()=>{\n            const state = get();\n            return {\n                projectDefinition: state.projectDefinition,\n                contextMap: state.contextMap,\n                emotionalTone: state.emotionalTone,\n                technicalLayer: state.technicalLayer,\n                legalRisk: state.legalRisk\n            };\n        }\n    }), {\n    name: \"contextkit-storage\",\n    version: 1\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/contextStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e93baefb0a7b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/N2UzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU5M2JhZWZiMGE3YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/glass-effects.css":
/*!**************************************!*\
  !*** ./src/styles/glass-effects.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5e0b1f04c4ea\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsYXNzLWVmZmVjdHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29udGV4dGtpdC8uL3NyYy9zdHlsZXMvZ2xhc3MtZWZmZWN0cy5jc3M/NWRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVlMGIxZjA0YzRlYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/glass-effects.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_glass_effects_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/glass-effects.css */ \"(rsc)/./src/styles/glass-effects.css\");\n/* harmony import */ var _components_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ThemeProvider */ \"(rsc)/./src/components/ThemeProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ContextKit - AI Context Builder\",\n    description: \"Create organized, actionable context for AI-driven projects\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"antialiased font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeProvider__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ContextKit\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\settings\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\settings\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/ThemeProvider.tsx":
/*!******************************************!*\
  !*** ./src/components/ThemeProvider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0),
/* harmony export */   useTheme: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zustand","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsettings%2Fpage&page=%2Fsettings%2Fpage&appPaths=%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();