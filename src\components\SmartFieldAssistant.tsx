'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';
import { <PERSON>rk<PERSON>, Loader2, Copy, Check, Wand2 } from 'lucide-react';

interface SmartFieldAssistantProps {
  fieldName: string;
  fieldValue: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  context?: any;
  className?: string;
}

export default function SmartFieldAssistant({
  fieldName,
  fieldValue,
  onValueChange,
  placeholder,
  context,
  className = ''
}: SmartFieldAssistantProps) {
  const { currentLanguage, getActiveProviders, getAllData } = useContextStore();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSuggestions, setGeneratedSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [activeProviders, setActiveProviders] = useState<any[]>([]);
  const [mounted, setMounted] = useState(false);

  const isArabic = currentLanguage === 'ar';

  // تجنب مشاكل الهيدريشن
  useEffect(() => {
    setMounted(true);
    setActiveProviders(getActiveProviders());
  }, [getActiveProviders]);

  // تحقق من وجود مقدم خدمة صالح
  const hasValidProvider = mounted && activeProviders.some(p =>
    p.apiKey &&
    p.validationStatus === 'valid' &&
    p.selectedModels &&
    p.selectedModels.length > 0
  );

  const translations = {
    generateWithAI: isArabic ? '📄 توليد بالذكاء الاصطناعي' : '📄 Generate with AI',
    generating: isArabic ? 'جاري التوليد...' : 'Generating...',
    suggestions: isArabic ? 'اقتراحات ذكية' : 'Smart Suggestions',
    useThis: isArabic ? 'استخدام هذا' : 'Use This',
    copy: isArabic ? 'نسخ' : 'Copy',
    copied: isArabic ? 'تم النسخ' : 'Copied',
    noProviders: isArabic
      ? 'يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً'
      : 'Please configure an AI provider and select models in Settings first',
    error: isArabic ? 'حدث خطأ أثناء التوليد' : 'Error occurred during generation',
    tryAgain: isArabic ? 'حاول مرة أخرى' : 'Try Again',
    regenerate: isArabic ? 'إعادة توليد' : 'Regenerate'
  };

  const generateSuggestions = async () => {
    if (!hasValidProvider) {
      console.warn('No valid provider available:', { activeProviders, hasValidProvider });
      alert(translations.noProviders);
      return;
    }

    setIsGenerating(true);

    try {
      const allContext = getAllData();
      const provider = activeProviders.find(p => p.apiKey && p.validationStatus === 'valid');

      console.log('Using provider:', provider?.name, 'with model:', provider?.selectedModels[0]);

      if (!provider) {
        throw new Error('No valid provider found');
      }

      // إنشاء prompt ذكي بناءً على السياق والحقل
      const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);
      console.log('Generated prompt:', prompt);

      const requestBody = {
        providerId: provider.id,
        apiKey: provider.apiKey,
        model: provider.selectedModels[0] || 'gpt-3.5-turbo',
        messages: [
          { role: 'user', content: prompt }
        ],
        context: allContext,
        fieldName,
        language: currentLanguage,
        temperature: 0.8,
        maxTokens: 500
      };

      console.log('Sending request to API:', requestBody);

      const response = await fetch('/api/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('API Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('API Result:', result);

      if (result.success) {
        // استخدام أول اقتراح مباشرة في خانة الكتابة
        const suggestions = parseSuggestions(result.content);
        console.log('Parsed suggestions:', suggestions);

        if (suggestions.length > 0) {
          // وضع أول اقتراح في خانة الكتابة
          onValueChange(suggestions[0]);
          // حفظ باقي الاقتراحات للاستخدام لاحقاً
          setGeneratedSuggestions(suggestions);
        } else {
          const errorMsg = isArabic ? 'لم يتم العثور على اقتراحات مناسبة' : 'No suitable suggestions found';
          onValueChange(errorMsg);
        }
      } else {
        throw new Error(result.error || 'Generation failed');
      }
    } catch (error) {
      console.error('Generation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      onValueChange(`${translations.error}: ${errorMessage}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const createSmartPrompt = (fieldName: string, currentValue: string, context: any, isArabic: boolean): string => {
    const fieldPrompts: Record<string, { ar: string; en: string }> = {
      name: {
        ar: `اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:
- قصيرة وسهلة التذكر
- تعكس طبيعة المشروع
- مناسبة للجمهور المستهدف
- أصلية ومميزة`,
        en: `Suggest 3 creative and suitable project names. The names should be:
- Short and memorable
- Reflect the project nature
- Suitable for target audience
- Original and distinctive`
      },
      purpose: {
        ar: `اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:
- واضحة ومباشرة
- تركز على القيمة المضافة
- تجذب المستخدمين المستهدفين
- تميز المشروع عن المنافسين`,
        en: `Write 3 different versions describing the project purpose. They should be:
- Clear and direct
- Focus on added value
- Attract target users
- Differentiate from competitors`
      },
      targetUsers: {
        ar: `حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:
- الخصائص الديموغرافية
- الاحتياجات والتحديات
- السلوكيات والتفضيلات`,
        en: `Define 3 different target user groups. For each group mention:
- Demographic characteristics
- Needs and challenges
- Behaviors and preferences`
      },
      goals: {
        ar: `اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:
- محددة وقابلة للقياس
- قابلة للتحقيق وواقعية
- مرتبطة بالجدول الزمني
- تدعم رؤية المشروع`,
        en: `Suggest 3 sets of goals (short, medium, long-term). They should be:
- Specific and measurable
- Achievable and realistic
- Time-bound
- Support project vision`
      }
    };

    const fieldPrompt = fieldPrompts[fieldName];
    const basePrompt = fieldPrompt ? (isArabic ? fieldPrompt.ar : fieldPrompt.en) :
      (isArabic ? `اقترح 3 خيارات مختلفة لـ ${fieldName}` : `Suggest 3 different options for ${fieldName}`);

    const contextInfo = isArabic
      ? `السياق الحالي للمشروع:\n${JSON.stringify(context, null, 2)}\n\n`
      : `Current project context:\n${JSON.stringify(context, null, 2)}\n\n`;

    const currentValueInfo = currentValue
      ? (isArabic ? `القيمة الحالية: ${currentValue}\n\n` : `Current value: ${currentValue}\n\n`)
      : '';

    const instructions = isArabic
      ? `تعليمات:
1. قدم 3 اقتراحات مختلفة ومتنوعة
2. رقم كل اقتراح (1، 2، 3)
3. اجعل كل اقتراح في سطر منفصل
4. استخدم اللغة العربية الواضحة
5. اعتمد على السياق المتوفر لتحسين الاقتراحات`
      : `Instructions:
1. Provide 3 different and diverse suggestions
2. Number each suggestion (1, 2, 3)
3. Put each suggestion on a separate line
4. Use clear English
5. Use the available context to improve suggestions`;

    return `${contextInfo}${currentValueInfo}${basePrompt}\n\n${instructions}`;
  };

  const parseSuggestions = (content: string): string[] => {
    // تقسيم المحتوى إلى اقتراحات منفصلة
    const lines = content.split('\n').filter(line => line.trim());
    const suggestions: string[] = [];

    for (const line of lines) {
      // البحث عن الأسطر المرقمة أو التي تبدأ برقم
      if (/^\d+[.\-\)]\s*/.test(line.trim()) || /^[•\-\*]\s*/.test(line.trim())) {
        const cleaned = line.replace(/^\d+[.\-\)]\s*/, '').replace(/^[•\-\*]\s*/, '').trim();
        if (cleaned && cleaned.length > 10) {
          suggestions.push(cleaned);
        }
      } else if (line.trim().length > 20 && !line.includes(':') && suggestions.length < 3) {
        suggestions.push(line.trim());
      }
    }

    // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل
    if (suggestions.length === 0) {
      const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
      return sentences.slice(0, 3).map(s => s.trim());
    }

    return suggestions.slice(0, 3);
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const regenerateContent = async () => {
    if (generatedSuggestions.length > 1) {
      // استخدام الاقتراح التالي إذا كان متوفراً
      const currentIndex = generatedSuggestions.findIndex(s => s === fieldValue);
      const nextIndex = (currentIndex + 1) % generatedSuggestions.length;
      onValueChange(generatedSuggestions[nextIndex]);
    } else {
      // توليد محتوى جديد
      await generateSuggestions();
    }
  };

  // تجنب مشاكل الهيدريشن
  if (!mounted) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <button
          disabled
          className="relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 cursor-not-allowed font-arabic"
        >
          <Sparkles className="w-4 h-4" />
          {isArabic ? '📄 توليد بالذكاء الاصطناعي' : '📄 Generate with AI'}
        </button>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <button
        onClick={generateSuggestions}
        disabled={isGenerating || !hasValidProvider}
        className={`relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group ${hasValidProvider ? 'bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95' : 'bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30'} ${isGenerating ? 'animate-pulse scale-105' : ''}`}
        title={hasValidProvider ? translations.generateWithAI : translations.noProviders}
      >
        {/* Glass effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Shimmer effect */}
        {hasValidProvider && !isGenerating && (
          <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer" />
        )}

        <div className="relative flex items-center gap-2">
          {isGenerating ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Sparkles className="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" />
          )}
          <span className="font-arabic font-medium">
            {isGenerating ? translations.generating : translations.generateWithAI}
          </span>
        </div>
      </button>

      {/* زر إعادة التوليد - يظهر فقط إذا كان هناك محتوى مولد */}
      {fieldValue && generatedSuggestions.length > 0 && !isGenerating && (
        <button
          onClick={regenerateContent}
          className="relative flex items-center gap-2 px-3 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-orange-500/80 to-red-500/80 hover:from-orange-600/90 hover:to-red-600/90 text-white shadow-lg hover:shadow-xl hover:shadow-orange-500/25 hover:scale-105 active:scale-95"
          title={isArabic ? 'إعادة توليد' : 'Regenerate'}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          <div className="relative flex items-center gap-1">
            <Wand2 className="w-4 h-4 group-hover:rotate-180 transition-transform duration-500" />
            <span className="font-arabic font-medium">
              {isArabic ? 'إعادة توليد' : 'Regenerate'}
            </span>
          </div>
        </button>
      )}
    </div>
  );
}
