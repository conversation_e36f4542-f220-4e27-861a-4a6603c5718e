'use client';

import { useState, useEffect } from 'react';
import { useContextStore } from '@/store/contextStore';
import { <PERSON>rk<PERSON>, Loader2, Copy, Check, Wand2 } from 'lucide-react';

interface SmartFieldAssistantProps {
  fieldName: string;
  fieldValue: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  context?: any;
  className?: string;
}

export default function SmartFieldAssistant({
  fieldName,
  fieldValue,
  onValueChange,
  placeholder,
  context,
  className = ''
}: SmartFieldAssistantProps) {
  const { currentLanguage, getActiveProviders, getAllData } = useContextStore();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSuggestions, setGeneratedSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [activeProviders, setActiveProviders] = useState<any[]>([]);
  const [mounted, setMounted] = useState(false);

  const isArabic = currentLanguage === 'ar';

  // تجنب مشاكل الهيدريشن
  useEffect(() => {
    setMounted(true);
    setActiveProviders(getActiveProviders());
  }, [getActiveProviders]);
  // تحقق من وجود مقدم خدمة صالح
  const hasValidProvider = mounted && activeProviders.some(p =>
    p.apiKey &&
    p.validationStatus === 'valid' &&
    p.selectedModels &&
    p.selectedModels.length > 0
  );

  const translations = {
    generateWithAI: isArabic ? '📄 توليد بالذكاء الاصطناعي' : '📄 Generate with AI',
    generating: isArabic ? 'جاري التوليد...' : 'Generating...',
    suggestions: isArabic ? 'اقتراحات ذكية' : 'Smart Suggestions',
    useThis: isArabic ? 'استخدام هذا' : 'Use This',
    copy: isArabic ? 'نسخ' : 'Copy',
    copied: isArabic ? 'تم النسخ' : 'Copied',
    noProviders: isArabic
      ? 'يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً'
      : 'Please configure an AI provider and select models in Settings first',
    error: isArabic ? 'حدث خطأ أثناء التوليد' : 'Error occurred during generation',
    tryAgain: isArabic ? 'حاول مرة أخرى' : 'Try Again',
    regenerate: isArabic ? 'إعادة توليد' : 'Regenerate'
  };

  const generateSuggestions = async () => {
    if (!hasValidProvider) {
      console.warn('No valid provider available:', { activeProviders, hasValidProvider });
      alert(translations.noProviders);
      return;
    }

    setIsGenerating(true);
    setGeneratedSuggestions([]);
    setShowSuggestions(true);

    try {
      const allContext = getAllData();
      const provider = activeProviders.find(p => p.apiKey && p.validationStatus === 'valid');

      console.log('Using provider:', provider?.name, 'with model:', provider?.selectedModels[0]);

      if (!provider) {
        throw new Error('No valid provider found');
      }

      // إنشاء prompt ذكي بناءً على السياق والحقل
      const prompt = createSmartPrompt(fieldName, fieldValue, allContext, isArabic);
      console.log('Generated prompt:', prompt);

      const requestBody = {
        providerId: provider.id,
        apiKey: provider.apiKey,
        model: provider.selectedModels[0] || 'gpt-3.5-turbo',
        messages: [
          { role: 'user', content: prompt }
        ],
        context: allContext,
        fieldName,
        language: currentLanguage,
        temperature: 0.8,
        maxTokens: 500
      };

      console.log('Sending request to API:', requestBody);

      const response = await fetch('/api/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      console.log('API Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`API Error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      console.log('API Result:', result);

      if (result.success) {
        // تقسيم النتيجة إلى اقتراحات متعددة
        const suggestions = parseSuggestions(result.content);
        console.log('Parsed suggestions:', suggestions);
        setGeneratedSuggestions(suggestions);

        if (suggestions.length === 0) {
          setGeneratedSuggestions([isArabic ? 'لم يتم العثور على اقتراحات مناسبة' : 'No suitable suggestions found']);
        }
      } else {
        throw new Error(result.error || 'Generation failed');
      }
    } catch (error) {
      console.error('Generation error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setGeneratedSuggestions([`${translations.error}: ${errorMessage}`]);
    } finally {
      setIsGenerating(false);
    }
  };

  const createSmartPrompt = (fieldName: string, currentValue: string, context: any, isArabic: boolean): string => {
    const fieldPrompts: Record<string, { ar: string; en: string }> = {
      name: {
        ar: `اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:
- قصيرة وسهلة التذكر
- تعكس طبيعة المشروع
- مناسبة للجمهور المستهدف
- أصلية ومميزة`,
        en: `Suggest 3 creative and suitable project names. The names should be:
- Short and memorable
- Reflect the project nature
- Suitable for target audience
- Original and distinctive`
      },
      purpose: {
        ar: `اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:
- واضحة ومباشرة
- تركز على القيمة المضافة
- تجذب المستخدمين المستهدفين
- تميز المشروع عن المنافسين`,
        en: `Write 3 different versions describing the project purpose. They should be:
- Clear and direct
- Focus on added value
- Attract target users
- Differentiate from competitors`
      },
      targetUsers: {
        ar: `حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:
- الخصائص الديموغرافية
- الاحتياجات والتحديات
- السلوكيات والتفضيلات`,
        en: `Define 3 different target user groups. For each group mention:
- Demographic characteristics
- Needs and challenges
- Behaviors and preferences`
      },
      goals: {
        ar: `اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:
- محددة وقابلة للقياس
- قابلة للتحقيق وواقعية
- مرتبطة بالجدول الزمني
- تدعم رؤية المشروع`,
        en: `Suggest 3 sets of goals (short, medium, long-term). They should be:
- Specific and measurable
- Achievable and realistic
- Time-bound
- Support project vision`
      }
    };

    const fieldPrompt = fieldPrompts[fieldName];
    const basePrompt = fieldPrompt ? (isArabic ? fieldPrompt.ar : fieldPrompt.en) : 
      (isArabic ? `اقترح 3 خيارات مختلفة لـ ${fieldName}` : `Suggest 3 different options for ${fieldName}`);

    const contextInfo = isArabic 
      ? `السياق الحالي للمشروع:\n${JSON.stringify(context, null, 2)}\n\n`
      : `Current project context:\n${JSON.stringify(context, null, 2)}\n\n`;

    const currentValueInfo = currentValue 
      ? (isArabic ? `القيمة الحالية: ${currentValue}\n\n` : `Current value: ${currentValue}\n\n`)
      : '';

    const instructions = isArabic 
      ? `تعليمات:
1. قدم 3 اقتراحات مختلفة ومتنوعة
2. رقم كل اقتراح (1، 2، 3)
3. اجعل كل اقتراح في سطر منفصل
4. استخدم اللغة العربية الواضحة
5. اعتمد على السياق المتوفر لتحسين الاقتراحات`
      : `Instructions:
1. Provide 3 different and diverse suggestions
2. Number each suggestion (1, 2, 3)
3. Put each suggestion on a separate line
4. Use clear English
5. Use the available context to improve suggestions`;

    return `${contextInfo}${currentValueInfo}${basePrompt}\n\n${instructions}`;
  };

  const parseSuggestions = (content: string): string[] => {
    // تقسيم المحتوى إلى اقتراحات منفصلة
    const lines = content.split('\n').filter(line => line.trim());
    const suggestions: string[] = [];

    for (const line of lines) {
      // البحث عن الأسطر المرقمة أو التي تبدأ برقم
      if (/^\d+[.\-\)]\s*/.test(line.trim()) || /^[•\-\*]\s*/.test(line.trim())) {
        const cleaned = line.replace(/^\d+[.\-\)]\s*/, '').replace(/^[•\-\*]\s*/, '').trim();
        if (cleaned && cleaned.length > 10) {
          suggestions.push(cleaned);
        }
      } else if (line.trim().length > 20 && !line.includes(':') && suggestions.length < 3) {
        suggestions.push(line.trim());
      }
    }

    // إذا لم نجد اقتراحات مرقمة، نقسم النص إلى جمل
    if (suggestions.length === 0) {
      const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
      return sentences.slice(0, 3).map(s => s.trim());
    }

    return suggestions.slice(0, 3);
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const useSuggestion = (suggestion: string) => {
    onValueChange(suggestion);
    setShowSuggestions(false);
  };

  // تجنب مشاكل الهيدريشن
  if (!mounted) {
    return (
      <div className={`relative ${className}`}>
        <button
          disabled
          className="relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 cursor-not-allowed font-arabic"
        >
          <Sparkles className="w-4 h-4" />
          {isArabic ? '📄 توليد بالذكاء الاصطناعي' : '📄 Generate with AI'}
        </button>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Generate Button - Glass Design */}
      <button
        onClick={generateSuggestions}
        disabled={isGenerating || !hasValidProvider}
        className={`
          relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out
          backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group
          ${hasValidProvider
            ? `bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80
               hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90
               text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25
               hover:scale-105 active:scale-95`
            : `bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500
               cursor-not-allowed border-gray-300/30 dark:border-gray-600/30`
          }
          ${isGenerating ? 'animate-pulse scale-105' : ''}
        `}
        title={hasValidProvider ? translations.generateWithAI : translations.noProviders}
      >
        {/* Glass effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Shimmer effect */}
        {hasValidProvider && !isGenerating && (
          <div className="absolute inset-0 -skew-x-12 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 group-hover:animate-shimmer" />
        )}

        <div className="relative flex items-center gap-2">
          {isGenerating ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Sparkles className="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" />
          )}
          <span className="font-arabic font-medium">
            {isGenerating ? translations.generating : translations.generateWithAI}
          </span>
        </div>
      </button>

      {/* Suggestions Panel - محسن للعرض الأكبر */}
      {showSuggestions && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setShowSuggestions(false)}
        >
          <div
            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl shadow-2xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-600 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
                    <Sparkles className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white font-arabic">
                    {translations.suggestions}
                  </h4>
                </div>
                <button
                  onClick={() => setShowSuggestions(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {isGenerating ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <Loader2 className="w-8 h-8 animate-spin text-blue-500 mb-4" />
                  <span className="text-lg text-gray-600 dark:text-gray-400 font-arabic">
                    {translations.generating}
                  </span>
                  <p className="text-sm text-gray-500 dark:text-gray-500 mt-2 font-arabic">
                    {isArabic ? 'جاري إنشاء اقتراحات ذكية لك...' : 'Generating smart suggestions for you...'}
                  </p>
                </div>
              ) : generatedSuggestions.length > 0 ? (
                <div className="space-y-4">
                  {generatedSuggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      className="group p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/10 dark:hover:to-indigo-900/10 transition-all duration-300 hover:shadow-md"
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-lg flex items-center justify-center font-bold text-sm">
                          {index + 1}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-gray-900 dark:text-white mb-4 font-arabic leading-relaxed text-base">
                            {suggestion}
                          </p>
                          <div className="flex gap-2">
                            <button
                              onClick={() => useSuggestion(suggestion)}
                              className="relative flex items-center gap-2 px-4 py-2 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95 font-arabic"
                            >
                              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="relative flex items-center gap-2">
                                <Wand2 className="w-4 h-4 group-hover:rotate-12 transition-transform duration-300" />
                                {translations.useThis}
                              </div>
                            </button>
                            <button
                              onClick={() => copyToClipboard(suggestion, index)}
                              className="relative flex items-center gap-2 px-4 py-2 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95"
                            >
                              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                              <div className="relative flex items-center gap-2">
                                {copiedIndex === index ? (
                                  <Check className="w-4 h-4 text-green-300 animate-bounce" />
                                ) : (
                                  <Copy className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                                )}
                                {copiedIndex === index ? translations.copied : translations.copy}
                              </div>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))
              ) : (
                <div className="text-center py-12">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-2xl">⚠️</span>
                    </div>
                    <p className="text-lg text-gray-600 dark:text-gray-400 font-arabic mb-2">
                      {translations.error}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-500 font-arabic">
                      {isArabic ? 'حدث خطأ أثناء توليد الاقتراحات. يرجى المحاولة مرة أخرى.' : 'An error occurred while generating suggestions. Please try again.'}
                    </p>
                  </div>
                  <button
                    onClick={generateSuggestions}
                    className="relative px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95 font-arabic"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <span className="relative flex items-center gap-2">
                      <Sparkles className="w-4 h-4" />
                      {translations.tryAgain}
                    </span>
                  </button>
                </div>
              )}

              {/* Footer with Regenerate Button */}
              {generatedSuggestions.length > 0 && !isGenerating && (
                <div className="border-t border-gray-200 dark:border-gray-600 p-4 bg-gray-50 dark:bg-gray-800/50">
                  <button
                    onClick={generateSuggestions}
                    className="relative w-full flex items-center justify-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-purple-500/80 to-pink-600/80 hover:from-purple-600/90 hover:to-pink-700/90 text-white shadow-md hover:shadow-lg hover:shadow-purple-500/25 hover:scale-105 active:scale-95 font-arabic"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="relative flex items-center gap-2">
                      <Sparkles className="w-5 h-5 group-hover:rotate-180 transition-transform duration-500" />
                      {translations.regenerate}
                    </div>
                  </button>
                </div>
              )}
          </div>
        </div>
      )}
    </div>
  );
}
