"use strict";exports.id=762,exports.ids=[762],exports.modules={7506:(e,r,t)=>{t.d(r,{Z:()=>a});let a=(0,t(2881).Z)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},2561:(e,r,t)=>{t.d(r,{Z:()=>d});var a=t(326),s=t(2835),l=t(9190);function o({currentModule:e}){let{projectDefinition:r,contextMap:t,emotionalTone:s,technicalLayer:o,legalRisk:i,currentLanguage:n}=(0,l.l)(),d="ar"===n,c=[{key:"project-definition",name:"Project Definition",nameAr:"تعريف المشروع",emoji:"\uD83C\uDFAF",data:r,href:"/project-definition"},{key:"context-map",name:"Context Map",nameAr:"خريطة السياق",emoji:"\uD83D\uDDFA️",data:t,href:"/context-map"},{key:"emotional-tone",name:"Emotional Tone",nameAr:"النبرة العاطفية",emoji:"✨",data:s,href:"/emotional-tone"},{key:"technical-layer",name:"Technical Layer",nameAr:"الطبقة التقنية",emoji:"⚙️",data:o,href:"/technical-layer"},{key:"legal-risk",name:"Legal & Privacy",nameAr:"القانونية والخصوصية",emoji:"\uD83D\uDD12",data:i,href:"/legal-risk"}],m=e=>{let r=Object.keys(e).length,t=Object.values(e).filter(e=>e&&"string"==typeof e&&e.trim()).length;return r>0?t/r*100:0},g=c.reduce((e,r)=>e+m(r.data),0)/c.length;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:d?"تقدم المشروع":"Project Progress"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[Math.round(g),"% ",d?"مكتمل":"Complete"]})]}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-6",children:a.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${g}%`}})}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:c.map(r=>{let t=m(r.data),s=e===r.key;return a.jsx("a",{href:r.href,className:`p-3 rounded-lg border-2 transition-all hover:shadow-md ${s?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"}`,children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"text-2xl mb-2",children:r.emoji}),a.jsx("div",{className:"text-xs font-medium text-gray-900 dark:text-white mb-2",children:d?r.nameAr:r.name}),a.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mb-1",children:a.jsx("div",{className:`h-1 rounded-full transition-all duration-300 ${100===t?"bg-green-500":"bg-blue-500"}`,style:{width:`${t}%`}})}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[Math.round(t),"%"]})]})},r.key)})}),(0,a.jsxs)("div",{className:"mt-6 flex justify-center space-x-4",children:[(0,a.jsxs)("a",{href:"/final-preview",className:"flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors",children:[a.jsx("span",{className:"mr-1",children:"\uD83D\uDCCB"}),d?"المعاينة النهائية":"Final Preview"]}),g>0&&(0,a.jsxs)("button",{onClick:()=>{confirm(d?"هل أنت متأكد من إعادة تعيين جميع البيانات؟":"Are you sure you want to reset all data?")&&window.location.reload()},className:"flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors",children:[a.jsx("span",{className:"mr-1",children:"\uD83D\uDD04"}),d?"إعادة تعيين":"Reset"]})]})]})}var i=t(7577);function n(){let[e,r]=(0,i.useState)(null),[t,s]=(0,i.useState)(!1),{currentLanguage:o}=(0,l.l)(),n="ar"===o;return e||t?a.jsx("div",{className:"fixed bottom-4 right-4 z-50",children:a.jsx("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg px-4 py-2 flex items-center space-x-2",children:t?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),a.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:n?"جاري الحفظ...":"Saving..."})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[n?"تم الحفظ":"Saved"," ",e?.toLocaleTimeString()]})]})})}):null}function d({title:e,titleAr:r,subtitle:t,subtitleAr:i,emoji:d,moduleKey:c,backLink:m,nextLink:g,children:u,rightPanel:h}){let{currentLanguage:x}=(0,l.l)(),p="ar"===x;return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[a.jsx(s.default,{title:p?r:e,subtitle:p?i:t,emoji:d,backLink:m?{href:m.href,label:p?m.labelAr:m.label}:void 0}),a.jsx(o,{currentModule:c}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto",children:[a.jsx("div",{className:`space-y-6 ${p?"lg:order-2":"lg:order-1"}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("span",{className:"text-2xl mr-3",children:"✍️"}),a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:p?"الأسئلة الذكية":"Smart Questions"})]}),u]})}),a.jsx("div",{className:`space-y-6 ${p?"lg:order-1":"lg:order-2"}`,children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 sticky top-8",children:[a.jsx("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC4"}),a.jsx("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:p?"المخرجات المجمّعة":"Generated Outputs"})]})}),h]})})]}),(m||g)&&(0,a.jsxs)("div",{className:"flex justify-between items-center mt-12 max-w-7xl mx-auto",children:[m?(0,a.jsxs)("a",{href:m.href,className:"flex items-center px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors",children:[a.jsx("span",{className:"mr-2",children:"←"}),p?m.labelAr:m.label]}):a.jsx("div",{}),g&&(0,a.jsxs)("a",{href:g.href,className:"flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:[p?g.labelAr:g.label,a.jsx("span",{className:"ml-2",children:"→"})]})]}),a.jsx(n,{})]})})}},9740:(e,r,t)=>{t.d(r,{Z:()=>o});var a=t(326),s=t(7577),l=t(9190);function o({moduleData:e,moduleName:r,moduleNameAr:t}){let{currentLanguage:o,outputFormat:i,setOutputFormat:n}=(0,l.l)(),[d,c]=(0,s.useState)(!1),m="ar"===o,g=()=>{let a=`# ${m?t:r}

`;return Object.entries(e).forEach(([e,r])=>{if(r&&"string"==typeof r&&r.trim()){let t=e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());a+=`## ${t}
${r}

`}}),a},u=()=>{let a=`<div class="module-output">
  <h1>${m?t:r}</h1>
`;return Object.entries(e).forEach(([e,r])=>{if(r&&"string"==typeof r&&r.trim()){let t=e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase());a+=`  <section>
    <h2>${t}</h2>
    <p>${r}</p>
  </section>
`}}),a+="</div>"},h=()=>JSON.stringify({module:m?t:r,data:Object.fromEntries(Object.entries(e).filter(([e,r])=>r&&"string"==typeof r&&r.trim())),metadata:{timestamp:new Date().toISOString(),language:m?"ar":"en",version:"1.0"}},null,2),x=()=>{let a=Object.fromEntries(Object.entries(e).filter(([e,r])=>r&&"string"==typeof r&&r.trim())),s=`# ${m?t:r}
`;return s+=`# Generated: ${new Date().toISOString()}

`,Object.entries(a).forEach(([e,r])=>{let t=e.replace(/([A-Z])/g,"_$1").toLowerCase();s+=`${t}: |
`,String(r||"").split("\n").forEach(e=>{s+=`  ${e}
`}),s+="\n"}),s},p=()=>{switch(i){case"markdown":default:return g();case"html":return u();case"json":return h();case"yaml":return x()}},b=async()=>{let e=p();await navigator.clipboard.writeText(e),c(!0),setTimeout(()=>c(!1),2e3)},f=Object.values(e).some(e=>e&&"string"==typeof e&&e.trim());return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("div",{className:"flex space-x-2 flex-wrap gap-2",children:["markdown","html","json","yaml"].map(e=>a.jsx("button",{onClick:()=>n(e),className:`px-3 py-1 text-sm rounded-lg transition-colors ${i===e?"bg-blue-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"}`,children:e.toUpperCase()},e))}),f&&(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:b,className:"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white shadow-md hover:shadow-lg hover:shadow-green-500/25 hover:scale-105 active:scale-95",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-1",children:[a.jsx("span",{className:d?"animate-bounce":"group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCCB"}),d?m?"تم النسخ!":"Copied!":m?"نسخ الكل":"Copy All"]})]}),(0,a.jsxs)("button",{onClick:()=>{let e=p(),t=`${r.toLowerCase().replace(/\s+/g,"-")}.${{markdown:"md",html:"html",json:"json",yaml:"yml"}[i]||"txt"}`,a=new Blob([e],{type:{markdown:"text/markdown",html:"text/html",json:"application/json",yaml:"text/yaml"}[i]||"text/plain"}),s=URL.createObjectURL(a),l=document.createElement("a");l.href=s,l.download=t,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(s)},className:"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white shadow-md hover:shadow-lg hover:shadow-blue-500/25 hover:scale-105 active:scale-95",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-1",children:[a.jsx("span",{className:"group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCBE"}),m?"تحميل":"Download"]})]})]})]}),a.jsx("div",{className:"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[300px]",children:f?a.jsx("pre",{className:`text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono overflow-x-auto ${m?"text-right":"text-left"}`,children:p()}):a.jsx("div",{className:"flex items-center justify-center h-full text-gray-500 dark:text-gray-400",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("span",{className:"text-4xl mb-2 block",children:"\uD83D\uDCDD"}),a.jsx("p",{children:m?"ابدأ بالإجابة على الأسئلة لرؤية المخرجات":"Start answering questions to see outputs"})]})})}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["markdown"===i&&(m?"تنسيق Markdown - جاهز للاستخدام في المستندات":"Markdown format - Ready for documentation"),"html"===i&&(m?"تنسيق HTML - جاهز للمواقع الإلكترونية":"HTML format - Ready for websites"),"json"===i&&(m?"تنسيق JSON - جاهز للبرمجة والـ APIs":"JSON format - Ready for programming and APIs"),"yaml"===i&&(m?"تنسيق YAML - جاهز للتكوين والنشر":"YAML format - Ready for configuration and deployment")]})]})}},9361:(e,r,t)=>{t.d(r,{Z:()=>d});var a=t(326),s=t(7577),l=t(9190);let o=(0,t(2881).Z)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);var i=t(7506);function n({fieldName:e,fieldValue:r,onValueChange:t,placeholder:n,context:d,className:c=""}){let{currentLanguage:m,getActiveProviders:g,getAllData:u}=(0,l.l)(),[h,x]=(0,s.useState)(!1),[p,b]=(0,s.useState)([]),[f,y]=(0,s.useState)(!1),[v,j]=(0,s.useState)(null),[w,N]=(0,s.useState)([]),[k,D]=(0,s.useState)(!1),C="ar"===m,$=k&&w.some(e=>e.apiKey&&"valid"===e.validationStatus&&e.selectedModels&&e.selectedModels.length>0),S={generateWithAI:C?"\uD83D\uDCC4 توليد بالذكاء الاصطناعي":"\uD83D\uDCC4 Generate with AI",generating:C?"جاري التوليد...":"Generating...",noProviders:C?"يرجى إعداد مقدم خدمة AI وتحديد النماذج في صفحة الإعدادات أولاً":"Please configure an AI provider and select models in Settings first",error:C?"حدث خطأ أثناء التوليد":"Error occurred during generation"},A=async()=>{if(!$){console.warn("No valid provider available:",{activeProviders:w,hasValidProvider:$}),alert(S.noProviders);return}x(!0),b([]),y(!0);try{let t=u(),a=w.find(e=>e.apiKey&&"valid"===e.validationStatus);if(console.log("Using provider:",a?.name,"with model:",a?.selectedModels[0]),!a)throw Error("No valid provider found");let s=P(e,r,t,C);console.log("Generated prompt:",s);let l={providerId:a.id,apiKey:a.apiKey,model:a.selectedModels[0]||"gpt-3.5-turbo",messages:[{role:"user",content:s}],context:t,fieldName:e,language:m,temperature:.8,maxTokens:500};console.log("Sending request to API:",l);let o=await fetch("/api/llm/generate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(console.log("API Response status:",o.status),!o.ok){let e=await o.text();throw console.error("API Error:",e),Error(`API Error: ${o.status} - ${e}`)}let i=await o.json();if(console.log("API Result:",i),i.success){let e=O(i.content);console.log("Parsed suggestions:",e),b(e),0===e.length&&b([C?"لم يتم العثور على اقتراحات مناسبة":"No suitable suggestions found"])}else throw Error(i.error||"Generation failed")}catch(r){console.error("Generation error:",r);let e=r instanceof Error?r.message:"Unknown error";b([`${S.error}: ${e}`])}finally{x(!1)}},P=(e,r,t,a)=>{let s={name:{ar:`اقترح 3 أسماء إبداعية ومناسبة للمشروع. يجب أن تكون الأسماء:
- قصيرة وسهلة التذكر
- تعكس طبيعة المشروع
- مناسبة للجمهور المستهدف
- أصلية ومميزة`,en:`Suggest 3 creative and suitable project names. The names should be:
- Short and memorable
- Reflect the project nature
- Suitable for target audience
- Original and distinctive`},purpose:{ar:`اكتب 3 صيغ مختلفة لوصف الغرض من المشروع. يجب أن تكون:
- واضحة ومباشرة
- تركز على القيمة المضافة
- تجذب المستخدمين المستهدفين
- تميز المشروع عن المنافسين`,en:`Write 3 different versions describing the project purpose. They should be:
- Clear and direct
- Focus on added value
- Attract target users
- Differentiate from competitors`},targetUsers:{ar:`حدد 3 مجموعات مختلفة من المستخدمين المستهدفين. لكل مجموعة اذكر:
- الخصائص الديموغرافية
- الاحتياجات والتحديات
- السلوكيات والتفضيلات`,en:`Define 3 different target user groups. For each group mention:
- Demographic characteristics
- Needs and challenges
- Behaviors and preferences`},goals:{ar:`اقترح 3 مجموعات من الأهداف (قصيرة، متوسطة، طويلة المدى). يجب أن تكون:
- محددة وقابلة للقياس
- قابلة للتحقيق وواقعية
- مرتبطة بالجدول الزمني
- تدعم رؤية المشروع`,en:`Suggest 3 sets of goals (short, medium, long-term). They should be:
- Specific and measurable
- Achievable and realistic
- Time-bound
- Support project vision`}}[e],l=s?a?s.ar:s.en:a?`اقترح 3 خيارات مختلفة لـ ${e}`:`Suggest 3 different options for ${e}`,o=a?`السياق الحالي للمشروع:
${JSON.stringify(t,null,2)}

`:`Current project context:
${JSON.stringify(t,null,2)}

`,i=r?a?`القيمة الحالية: ${r}

`:`Current value: ${r}

`:"",n=a?`تعليمات:
1. قدم 3 اقتراحات مختلفة ومتنوعة
2. رقم كل اقتراح (1، 2، 3)
3. اجعل كل اقتراح في سطر منفصل
4. استخدم اللغة العربية الواضحة
5. اعتمد على السياق المتوفر لتحسين الاقتراحات`:`Instructions:
1. Provide 3 different and diverse suggestions
2. Number each suggestion (1, 2, 3)
3. Put each suggestion on a separate line
4. Use clear English
5. Use the available context to improve suggestions`;return`${o}${i}${l}

${n}`},O=e=>{let r=e.split("\n").filter(e=>e.trim()),t=[];for(let e of r)if(/^\d+[.\-\)]\s*/.test(e.trim())||/^[•\-\*]\s*/.test(e.trim())){let r=e.replace(/^\d+[.\-\)]\s*/,"").replace(/^[•\-\*]\s*/,"").trim();r&&r.length>10&&t.push(r)}else e.trim().length>20&&!e.includes(":")&&t.length<3&&t.push(e.trim());return 0===t.length?e.split(/[.!?]+/).filter(e=>e.trim().length>20).slice(0,3).map(e=>e.trim()):t.slice(0,3)};return k?a.jsx("div",{className:`relative ${c}`,children:a.jsx("button",{onClick:A,disabled:h||!$,className:`relative flex items-center gap-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group ${$?"bg-gradient-to-br from-purple-500/80 via-blue-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:via-blue-600/90 hover:to-indigo-700/90 text-white shadow-lg hover:shadow-xl hover:shadow-purple-500/25 hover:scale-105 active:scale-95":"bg-gray-200/50 dark:bg-gray-700/50 text-gray-400 dark:text-gray-500 cursor-not-allowed border-gray-300/30 dark:border-gray-600/30"} ${h?"animate-pulse scale-105":""}`,title:$?S.generateWithAI:S.noProviders,children:(0,a.jsxs)("div",{className:"relative flex items-center gap-2",children:[h?a.jsx(i.Z,{className:"w-4 h-4 animate-spin"}):a.jsx(o,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),a.jsx("span",{className:"font-arabic font-medium",children:h?S.generating:S.generateWithAI})]})})}):a.jsx("div",{className:`relative ${c}`,children:(0,a.jsxs)("button",{disabled:!0,className:"relative flex items-center gap-2 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-blue-500/80 to-indigo-600/80 text-white shadow-md opacity-50 cursor-not-allowed font-arabic",children:[a.jsx(o,{className:"w-4 h-4"}),C?"\uD83D\uDCC4 توليد بالذكاء الاصطناعي":"\uD83D\uDCC4 Generate with AI"]})})}function d({id:e,question:r,questionAr:t,placeholder:o,placeholderAr:i,value:d,onChange:c,type:m="textarea",aiSuggestion:g,aiSuggestionAr:u,promptTemplate:h}){let{currentLanguage:x}=(0,l.l)(),[p,b]=(0,s.useState)(!1),[f,y]=(0,s.useState)(!1),v="ar"===x,j=async()=>{d.trim()&&(await navigator.clipboard.writeText(d),y(!0),setTimeout(()=>y(!1),2e3))},w=async()=>{if(h&&d.trim()){let e=h.replace("{answer}",d);await navigator.clipboard.writeText(e),y(!0),setTimeout(()=>y(!1),2e3)}};return(0,a.jsxs)("div",{className:"space-y-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:v?t:r}),(g||u)&&(0,a.jsxs)("button",{onClick:()=>b(!p),className:"text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center",children:[a.jsx("span",{className:"mr-1",children:"\uD83E\uDDE0"}),v?"عرض الاقتراح الذكي":"Show AI Suggestion"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[a.jsx(n,{fieldName:e,fieldValue:d,onValueChange:c,placeholder:v?i:o,className:"flex-shrink-0"}),d.trim()&&(0,a.jsxs)("button",{onClick:j,className:"relative flex items-center px-3 py-1.5 text-sm rounded-lg font-medium transition-all duration-300 ease-in-out backdrop-blur-md border border-white/20 dark:border-gray-700/50 overflow-hidden group bg-gradient-to-br from-gray-500/80 to-slate-600/80 hover:from-gray-600/90 hover:to-slate-700/90 text-white shadow-md hover:shadow-lg hover:shadow-gray-500/25 hover:scale-105 active:scale-95",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,a.jsxs)("div",{className:"relative flex items-center gap-1",children:[a.jsx("span",{className:f?"animate-bounce":"group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCCE"}),f?v?"تم النسخ!":"Copied!":v?"نسخ كـ Prompt":"Copy as Prompt"]})]})]})]}),p&&(g||u)&&a.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex items-start",children:[a.jsx("span",{className:"text-blue-500 mr-2",children:"\uD83D\uDCA1"}),a.jsx("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:v?u:g})]})}),"textarea"===m?a.jsx("textarea",{value:d,onChange:e=>c(e.target.value),placeholder:v?i:o,className:`w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none ${v?"text-right":"text-left"}`,rows:4,dir:v?"rtl":"ltr"}):a.jsx("input",{type:"text",value:d,onChange:e=>c(e.target.value),placeholder:v?i:o,className:`w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white ${v?"text-right":"text-left"}`,dir:v?"rtl":"ltr"}),h&&d.trim()&&a.jsx("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{onClick:w,className:"flex items-center px-4 py-2 text-sm bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/30 transition-colors",children:[a.jsx("span",{className:"mr-1",children:"\uD83D\uDE80"}),v?"نسخ كـ Prompt":"Copy as Prompt"]})})]})}}};