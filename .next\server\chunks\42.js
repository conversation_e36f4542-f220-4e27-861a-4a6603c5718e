exports.id=42,exports.ids=[42],exports.modules={2829:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},2203:(e,t,r)=>{Promise.resolve().then(r.bind(r,4609))},2835:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(326),o=r(434);let i=(0,r(2881).Z)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var n=r(8378),s=r(4233),l=r(5130),d=r(9190);function c({title:e,subtitle:t,backLink:r,emoji:c}){let{currentLanguage:g}=(0,d.l)(),u="ar"===g;return(0,a.jsxs)("header",{className:"relative",children:[a.jsx("div",{className:`fixed top-4 ${u?"left-4":"right-4"} z-50 flex gap-3`,children:a.jsx("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 p-2",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(o.default,{href:"/",className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden",title:u?"الصفحة الرئيسية":"Home",children:[a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx(i,{className:"w-6 h-6 text-gray-600 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors"})}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,a.jsxs)(o.default,{href:"/settings",className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-purple-50 to-violet-100 dark:from-gray-800 dark:to-gray-900 hover:from-purple-100 hover:to-violet-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden",title:u?"إعدادات API":"API Settings",children:[a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx(n.Z,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 transition-all duration-300 group-hover:rotate-90"})}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),a.jsx(l.Z,{}),a.jsx(s.Z,{})]})})}),(0,a.jsxs)("div",{className:"text-center mb-8 pt-4",children:[r&&a.jsx(o.default,{href:r.href,className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 inline-block transition-colors",children:r.label}),(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-2",children:[c&&a.jsx("span",{className:"mr-2",children:c}),e]}),t&&a.jsx("p",{className:"text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:t})]})]})}},5130:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(326),o=r(9190);function i(){let{currentLanguage:e,setLanguage:t}=(0,o.l)();return(0,a.jsxs)("button",{onClick:()=>t("ar"===e?"en":"ar"),className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-800 dark:to-gray-900 hover:from-green-100 hover:to-emerald-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":"ar"===e?"Switch to English":"التبديل إلى العربية",title:"ar"===e?"Switch to English":"التبديل إلى العربية",children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[a.jsx("span",{className:`text-sm font-bold text-blue-600 dark:text-blue-400 transition-all duration-500 ease-in-out ${"en"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"}`,children:"EN"}),a.jsx("span",{className:`text-sm font-bold text-green-600 dark:text-green-400 absolute transition-all duration-500 ease-in-out font-arabic ${"ar"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"}`,children:"عر"})]}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}},4609:(e,t,r)=>{"use strict";r.d(t,{F:()=>s,ThemeProvider:()=>n});var a=r(326),o=r(7577);let i=(0,o.createContext)(void 0);function n({children:e}){let[t,r]=(0,o.useState)("light"),[n,s]=(0,o.useState)(!1);return n?a.jsx(i.Provider,{value:{theme:t,toggleTheme:()=>{r(e=>"light"===e?"dark":"light")}},children:e}):a.jsx(a.Fragment,{children:e})}function s(){let e=(0,o.useContext)(i);return void 0===e?{theme:"light",toggleTheme:()=>{}}:e}},4233:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(326),o=r(4609);function i(){let{theme:e,toggleTheme:t}=(0,o.F)();return(0,a.jsxs)("button",{onClick:t,className:"relative w-12 h-12 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 hover:from-blue-100 hover:to-indigo-200 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-600 group overflow-hidden","aria-label":`Switch to ${"light"===e?"dark":"light"} mode`,title:`Switch to ${"light"===e?"dark":"light"} mode`,children:[(0,a.jsxs)("div",{className:"absolute inset-0 flex items-center justify-center",children:[a.jsx("svg",{className:`w-6 h-6 text-amber-500 transition-all duration-500 ease-in-out ${"light"===e?"opacity-100 rotate-0 scale-100":"opacity-0 rotate-180 scale-0"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),a.jsx("svg",{className:`w-6 h-6 text-blue-400 absolute transition-all duration-500 ease-in-out ${"dark"===e?"opacity-100 rotate-0 scale-100":"opacity-0 -rotate-180 scale-0"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]})}},9190:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});var a=r(8408),o=r(5251);let i={projectDefinition:{name:"",purpose:"",targetUsers:"",goals:"",scope:"",timeline:""},contextMap:{timeContext:"",language:"",location:"",culturalContext:"",behavioralAspects:"",environmentalFactors:""},emotionalTone:{personality:"",communicationStyle:"",userExperience:"",brandVoice:"",emotionalIntelligence:"",interactionFlow:""},technicalLayer:{programmingLanguages:"",frameworks:"",llmModels:"",databases:"",apis:"",infrastructure:""},legalRisk:{privacyConcerns:"",dataProtection:"",compliance:"",risks:"",mitigation:"",ethicalConsiderations:""},currentLanguage:"ar",outputFormat:"markdown",apiSettings:{providers:[],globalSettings:{temperature:.7,topP:.9,maxTokens:1e3,timeout:3e4},openaiApiKey:"",openrouterApiKey:"",customModels:[]}},n=(0,a.U)()((0,o.tJ)((e,t)=>({...i,updateProjectDefinition:t=>e(e=>({projectDefinition:{...e.projectDefinition,...t}})),updateContextMap:t=>e(e=>({contextMap:{...e.contextMap,...t}})),updateEmotionalTone:t=>e(e=>({emotionalTone:{...e.emotionalTone,...t}})),updateTechnicalLayer:t=>e(e=>({technicalLayer:{...e.technicalLayer,...t}})),updateLegalRisk:t=>e(e=>({legalRisk:{...e.legalRisk,...t}})),setLanguage:t=>e({currentLanguage:t}),setOutputFormat:t=>e({outputFormat:t}),setApiSettings:t=>e({apiSettings:{...t,providers:t.providers||[]}}),addProvider:t=>e(e=>{let r=e.apiSettings.providers||[];return r.find(e=>e.id===t.id)?(console.warn(`Provider with id ${t.id} already exists`),e):{apiSettings:{...e.apiSettings,providers:[...r,t]}}}),updateProvider:(t,r)=>e(e=>({apiSettings:{...e.apiSettings,providers:(e.apiSettings.providers||[]).map(e=>e.id===t?{...e,...r}:e)}})),removeProvider:t=>e(e=>({apiSettings:{...e.apiSettings,providers:(e.apiSettings.providers||[]).filter(e=>e.id!==t),defaultProvider:e.apiSettings.defaultProvider===t?void 0:e.apiSettings.defaultProvider}})),validateProvider:async e=>{let r=t(),a=(r.apiSettings.providers||[]).find(t=>t.id===e);if(!a)return!1;try{r.updateProvider(e,{validationStatus:"pending",errorMessage:void 0});let t=await fetch("/api/llm/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({providerId:a.id,apiKey:a.apiKey,baseUrl:a.baseUrl})}),o=await t.json();if(o.valid)return r.updateProvider(e,{validationStatus:"valid",lastValidated:new Date,errorMessage:void 0,isEnabled:!0}),!0;return r.updateProvider(e,{validationStatus:"invalid",errorMessage:o.error||"Validation failed"}),!1}catch(t){return r.updateProvider(e,{validationStatus:"error",errorMessage:t instanceof Error?t.message:"Unknown error"}),!1}},getProvider:e=>(t().apiSettings.providers||[]).find(t=>t.id===e),getActiveProviders:()=>(t().apiSettings.providers||[]).filter(e=>e.isEnabled),setDefaultProvider:t=>e(e=>({apiSettings:{...e.apiSettings,defaultProvider:t}})),resetAll:()=>e(i),getModuleData:e=>{let r=t();switch(e){case"project":return r.projectDefinition;case"context":return r.contextMap;case"emotional":return r.emotionalTone;case"technical":return r.technicalLayer;case"legal":return r.legalRisk;default:return{}}},getAllData:()=>{let e=t();return{projectDefinition:e.projectDefinition,contextMap:e.contextMap,emotionalTone:e.emotionalTone,technicalLayer:e.technicalLayer,legalRisk:e.legalRisk}}}),{name:"contextkit-storage",version:1}))},6157:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var a=r(9510);r(5023),r(3490);var o=r(8570);let i=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx`),{__esModule:n,$$typeof:s}=i;i.default;let l=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#ThemeProvider`);(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\components\ThemeProvider.tsx#useTheme`);let d={title:"ContextKit - AI Context Builder",description:"Create organized, actionable context for AI-driven projects"};function c({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),a.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),a.jsx("body",{className:"antialiased font-arabic",children:a.jsx(l,{children:e})})]})}},5023:()=>{},3490:()=>{}};