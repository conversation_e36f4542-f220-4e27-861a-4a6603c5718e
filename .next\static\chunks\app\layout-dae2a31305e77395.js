(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{7165:function(e,t,n){Promise.resolve().then(n.t.bind(n,3054,23)),Promise.resolve().then(n.bind(n,8546)),Promise.resolve().then(n.t.bind(n,791,23))},8546:function(e,t,n){"use strict";n.d(t,{F:function(){return c},ThemeProvider:function(){return s}});var r=n(7437),o=n(2265);let i=(0,o.createContext)(void 0);function s(e){let{children:t}=e,[n,s]=(0,o.useState)("light"),[c,l]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";s(e||t),l(!0)},[]),(0,o.useEffect)(()=>{if(c){let e=document.documentElement;"dark"===n?e.classList.add("dark"):e.classList.remove("dark"),localStorage.setItem("theme",n)}},[n,c]),c)?(0,r.jsx)(i.Provider,{value:{theme:n,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")}},children:t}):(0,r.jsx)(r.Fragment,{children:t})}function c(){let e=(0,o.useContext)(i);return void 0===e?{theme:"light",toggleTheme:()=>{}}:e}},3054:function(){},791:function(){}},function(e){e.O(0,[838,971,23,744],function(){return e(e.s=7165)}),_N_E=e.O()}]);