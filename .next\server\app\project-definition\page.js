(()=>{var e={};e.id=532,e.ids=[532],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2468:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l}),r(9913),r(6157),r(5866);var o=r(3191),i=r(8716),s=r(7922),a=r.n(s),n=r(5231),p={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(t,p);let l=["",{children:["project-definition",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9913)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,6157)),"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\project-definition\\page.tsx"],u="/project-definition/page",d={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/project-definition/page",pathname:"/project-definition",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},7844:(e,t,r)=>{Promise.resolve().then(r.bind(r,4433))},4433:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var o=r(326),i=r(2561),s=r(9361),a=r(9740),n=r(9190);function p(){let{projectDefinition:e,updateProjectDefinition:t}=(0,n.l)(),r=(e,r)=>{t({[e]:r})};return o.jsx(i.Z,{title:"Project Definition",titleAr:"تعريف المشروع",subtitle:"Define the scope, users, and goals of your AI project",subtitleAr:"حدد نطاق مشروعك والمستخدمين والأهداف",emoji:"\uD83C\uDFAF",moduleKey:"project-definition",backLink:{href:"/",label:"← Back to Home",labelAr:"← العودة للرئيسية"},nextLink:{href:"/context-map",label:"Next: Context Map →",labelAr:"التالي: خريطة السياق ←"},rightPanel:o.jsx(a.Z,{moduleData:e,moduleName:"Project Definition",moduleNameAr:"تعريف المشروع"}),children:o.jsx("div",{className:"space-y-6",children:[{id:"name",question:"What is the name of your AI project?",questionAr:"ما هو اسم مشروع الذكاء الاصطناعي الخاص بك؟",placeholder:"e.g., Smart Customer Support Bot, Content Generator AI, etc.",placeholderAr:"مثال: بوت دعم العملاء الذكي، مولد المحتوى بالذكاء الاصطناعي، إلخ.",type:"text",aiSuggestion:"Choose a clear, descriptive name that reflects your project's main function and target audience.",aiSuggestionAr:"اختر اسماً واضحاً ووصفياً يعكس الوظيفة الرئيسية لمشروعك والجمهور المستهدف.",promptTemplate:'Help me refine this AI project name: "{answer}". Suggest improvements for clarity and market appeal.'},{id:"purpose",question:"What is the main purpose or problem your AI project aims to solve?",questionAr:"ما هو الهدف الرئيسي أو المشكلة التي يهدف مشروع الذكاء الاصطناعي لحلها؟",placeholder:"Describe the core problem you want to address...",placeholderAr:"صف المشكلة الأساسية التي تريد معالجتها...",aiSuggestion:"Focus on a specific, measurable problem. Avoid being too broad or vague.",aiSuggestionAr:"ركز على مشكلة محددة وقابلة للقياس. تجنب أن تكون عاماً أو غامضاً.",promptTemplate:'Analyze this problem statement for an AI project: "{answer}". Help me make it more specific and actionable.'},{id:"targetUsers",question:"Who are the primary users or beneficiaries of this project?",questionAr:"من هم المستخدمون الأساسيون أو المستفيدون من هذا المشروع؟",placeholder:"e.g., Customer service teams, Content creators, Students, etc.",placeholderAr:"مثال: فرق خدمة العملاء، منشئو المحتوى، الطلاب، إلخ.",aiSuggestion:"Be specific about user demographics, roles, and their current pain points.",aiSuggestionAr:"كن محدداً حول التركيبة السكانية للمستخدمين وأدوارهم ونقاط الألم الحالية لديهم.",promptTemplate:'Help me create detailed user personas for this target audience: "{answer}". Include their needs and challenges.'}].map(t=>o.jsx(s.Z,{id:t.id,question:t.question,questionAr:t.questionAr,placeholder:t.placeholder,placeholderAr:t.placeholderAr,value:e[t.id]||"",onChange:e=>r(t.id,e),type:t.type,aiSuggestion:t.aiSuggestion,aiSuggestionAr:t.aiSuggestionAr,promptTemplate:t.promptTemplate},t.id))})})}},9913:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>s,default:()=>n});var o=r(8570);let i=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\project-definition\page.tsx`),{__esModule:s,$$typeof:a}=i;i.default;let n=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\ContextKit\src\app\project-definition\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[948,471,634,42,762],()=>r(2468));module.exports=o})();