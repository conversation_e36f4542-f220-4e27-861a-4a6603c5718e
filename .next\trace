[{"name": "generate-buildid", "duration": 426, "timestamp": 53756772171, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751774597487, "traceId": "d9a17e1b15669dd7"}, {"name": "load-custom-routes", "duration": 457, "timestamp": 53756772932, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751774597488, "traceId": "d9a17e1b15669dd7"}, {"name": "create-pages-mapping", "duration": 464, "timestamp": 53756954276, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751774597669, "traceId": "d9a17e1b15669dd7"}, {"name": "collect-app-paths", "duration": 8087, "timestamp": 53756954803, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751774597670, "traceId": "d9a17e1b15669dd7"}, {"name": "create-app-mapping", "duration": 2896, "timestamp": 53756962942, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751774597678, "traceId": "d9a17e1b15669dd7"}, {"name": "public-dir-conflict-check", "duration": 3181, "timestamp": 53756967056, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751774597682, "traceId": "d9a17e1b15669dd7"}, {"name": "generate-routes-manifest", "duration": 9843, "timestamp": 53756971002, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751774597686, "traceId": "d9a17e1b15669dd7"}, {"name": "create-dist-dir", "duration": 1760, "timestamp": 53756984008, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751774597699, "traceId": "d9a17e1b15669dd7"}, {"name": "write-routes-manifest", "duration": 8289, "timestamp": 53757070507, "id": 12, "parentId": 1, "tags": {}, "startTime": 1751774597785, "traceId": "d9a17e1b15669dd7"}, {"name": "generate-required-server-files", "duration": 1152, "timestamp": 53757087095, "id": 13, "parentId": 1, "tags": {}, "startTime": 1751774597802, "traceId": "d9a17e1b15669dd7"}, {"name": "create-entrypoints", "duration": 155120, "timestamp": 53759954434, "id": 17, "parentId": 15, "tags": {}, "startTime": 1751774600669, "traceId": "d9a17e1b15669dd7"}, {"name": "generate-webpack-config", "duration": 1361242, "timestamp": 53760110651, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751774600825, "traceId": "d9a17e1b15669dd7"}, {"name": "next-trace-entrypoint-plugin", "duration": 4129, "timestamp": 53761677021, "id": 20, "parentId": 19, "tags": {}, "startTime": 1751774602392, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 372489, "timestamp": 53761693732, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751774602408, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 387896, "timestamp": 53761693825, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 423157, "timestamp": 53761694678, "id": 39, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741525, "timestamp": 53761694659, "id": 37, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fsettings%2Fpage&name=app%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 743845, "timestamp": 53761692388, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602407, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 742276, "timestamp": 53761693972, "id": 30, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fcontext-map%2Fpage&name=app%2Fcontext-map%2Fpage&pagePath=private-next-app-dir%2Fcontext-map%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fcontext-map%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741980, "timestamp": 53761694463, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ffinal-preview%2Fpage&name=app%2Ffinal-preview%2Fpage&pagePath=private-next-app-dir%2Ffinal-preview%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Ffinal-preview%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741902, "timestamp": 53761694575, "id": 32, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Femotional-tone%2Fpage&name=app%2Femotional-tone%2Fpage&pagePath=private-next-app-dir%2Femotional-tone%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Femotional-tone%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741897, "timestamp": 53761694593, "id": 33, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Flegal-risk%2Fpage&name=app%2Flegal-risk%2Fpage&pagePath=private-next-app-dir%2Flegal-risk%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Flegal-risk%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741909, "timestamp": 53761694601, "id": 34, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741911, "timestamp": 53761694607, "id": 35, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fproject-definition%2Fpage&name=app%2Fproject-definition%2Fpage&pagePath=private-next-app-dir%2Fproject-definition%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fproject-definition%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741910, "timestamp": 53761694614, "id": 36, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ftechnical-layer%2Fpage&name=app%2Ftechnical-layer%2Fpage&pagePath=private-next-app-dir%2Ftechnical-layer%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Ftechnical-layer%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 741871, "timestamp": 53761694670, "id": 38, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fvibe%2Fpage&name=app%2Fvibe%2Fpage&pagePath=private-next-app-dir%2Fvibe%2Fpage.tsx&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fvibe%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 755578, "timestamp": 53761693932, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fopenrouter%2Froute&name=app%2Fapi%2Fopenrouter%2Froute&pagePath=private-next-app-dir%2Fapi%2Fopenrouter%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fapi%2Fopenrouter%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 755580, "timestamp": 53761693953, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fopenai%2Froute&name=app%2Fapi%2Fopenai%2Froute&pagePath=private-next-app-dir%2Fapi%2Fopenai%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fapi%2Fopenai%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 755681, "timestamp": 53761693861, "id": 25, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fllm%2Fmodels%2Froute&name=app%2Fapi%2Fllm%2Fmodels%2Froute&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fmodels%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fapi%2Fllm%2Fmodels%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 755658, "timestamp": 53761693891, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fllm%2Fgenerate%2Froute&name=app%2Fapi%2Fllm%2Fgenerate%2Froute&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fgenerate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fapi%2Fllm%2Fgenerate%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "add-entry", "duration": 755642, "timestamp": 53761693913, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fllm%2Fvalidate%2Froute&name=app%2Fapi%2Fllm%2Fvalidate%2Froute&pagePath=private-next-app-dir%2Fapi%2Fllm%2Fvalidate%2Froute.ts&appDir=C%3A%5CUsers%5Cfaiss%5CDesktop%5CContextKit%5Csrc%5Capp&appPaths=%2Fapi%2Fllm%2Fvalidate%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751774602409, "traceId": "d9a17e1b15669dd7"}, {"name": "next-swc-transform", "duration": 25940, "timestamp": 53762716376, "id": 90, "parentId": 89, "tags": {}, "startTime": 1751774603431, "traceId": "d9a17e1b15669dd7"}, {"name": "next-swc-loader", "duration": 27740, "timestamp": 53762714619, "id": 89, "parentId": 88, "tags": {}, "startTime": 1751774603429, "traceId": "d9a17e1b15669dd7"}, {"name": "build-module-tsx", "duration": 104578, "timestamp": 53762702180, "id": 88, "parentId": 19, "tags": {"name": "C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\components\\SmartFieldAssistant.tsx", "layer": "ssr"}, "startTime": 1751774603417, "traceId": "d9a17e1b15669dd7"}, {"name": "make", "duration": 1120191, "timestamp": 53761691498, "id": 21, "parentId": 19, "tags": {}, "startTime": 1751774602406, "traceId": "d9a17e1b15669dd7"}, {"name": "get-entries", "duration": 7885, "timestamp": 53762813756, "id": 92, "parentId": 91, "tags": {}, "startTime": 1751774603528, "traceId": "d9a17e1b15669dd7"}, {"name": "node-file-trace-plugin", "duration": 276396, "timestamp": 53762828175, "id": 93, "parentId": 91, "tags": {"traceEntryCount": "32"}, "startTime": 1751774603543, "traceId": "d9a17e1b15669dd7"}, {"name": "collect-traced-files", "duration": 2076, "timestamp": 53763104595, "id": 94, "parentId": 91, "tags": {}, "startTime": 1751774603819, "traceId": "d9a17e1b15669dd7"}, {"name": "finish-modules", "duration": 293235, "timestamp": 53762813454, "id": 91, "parentId": 20, "tags": {}, "startTime": 1751774603528, "traceId": "d9a17e1b15669dd7"}, {"name": "chunk-graph", "duration": 22172, "timestamp": 53763182672, "id": 96, "parentId": 95, "tags": {}, "startTime": 1751774603897, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-modules", "duration": 57, "timestamp": 53763205065, "id": 98, "parentId": 95, "tags": {}, "startTime": 1751774603920, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-chunks", "duration": 36019, "timestamp": 53763205273, "id": 99, "parentId": 95, "tags": {}, "startTime": 1751774603920, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-tree", "duration": 266, "timestamp": 53763241439, "id": 100, "parentId": 95, "tags": {}, "startTime": 1751774603956, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-chunk-modules", "duration": 21311, "timestamp": 53763241927, "id": 101, "parentId": 95, "tags": {}, "startTime": 1751774603957, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize", "duration": 58474, "timestamp": 53763204967, "id": 97, "parentId": 95, "tags": {}, "startTime": 1751774603920, "traceId": "d9a17e1b15669dd7"}, {"name": "module-hash", "duration": 45725, "timestamp": 53763332601, "id": 102, "parentId": 95, "tags": {}, "startTime": 1751774604047, "traceId": "d9a17e1b15669dd7"}, {"name": "code-generation", "duration": 43351, "timestamp": 53763378472, "id": 103, "parentId": 95, "tags": {}, "startTime": 1751774604093, "traceId": "d9a17e1b15669dd7"}, {"name": "hash", "duration": 18181, "timestamp": 53763432845, "id": 104, "parentId": 95, "tags": {}, "startTime": 1751774604148, "traceId": "d9a17e1b15669dd7"}, {"name": "code-generation-jobs", "duration": 1242, "timestamp": 53763451014, "id": 105, "parentId": 95, "tags": {}, "startTime": 1751774604166, "traceId": "d9a17e1b15669dd7"}, {"name": "module-assets", "duration": 871, "timestamp": 53763451940, "id": 106, "parentId": 95, "tags": {}, "startTime": 1751774604167, "traceId": "d9a17e1b15669dd7"}, {"name": "create-chunk-assets", "duration": 8057, "timestamp": 53763452845, "id": 107, "parentId": 95, "tags": {}, "startTime": 1751774604168, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11453, "timestamp": 53763496155, "id": 109, "parentId": 108, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11080, "timestamp": 53763496545, "id": 110, "parentId": 108, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11058, "timestamp": 53763496571, "id": 111, "parentId": 108, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11037, "timestamp": 53763496596, "id": 112, "parentId": 108, "tags": {"name": "../app/api/llm/models/route.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11027, "timestamp": 53763496620, "id": 113, "parentId": 108, "tags": {"name": "../app/api/llm/generate/route.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11011, "timestamp": 53763496639, "id": 114, "parentId": 108, "tags": {"name": "../app/api/llm/validate/route.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 11001, "timestamp": 53763496651, "id": 115, "parentId": 108, "tags": {"name": "../app/api/openrouter/route.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10992, "timestamp": 53763496664, "id": 116, "parentId": 108, "tags": {"name": "../app/api/openai/route.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10984, "timestamp": 53763496675, "id": 117, "parentId": 108, "tags": {"name": "../app/context-map/page.js", "cache": "HIT"}, "startTime": 1751774604211, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10818, "timestamp": 53763496844, "id": 118, "parentId": 108, "tags": {"name": "../app/final-preview/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10781, "timestamp": 53763496883, "id": 119, "parentId": 108, "tags": {"name": "../app/emotional-tone/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10764, "timestamp": 53763496903, "id": 120, "parentId": 108, "tags": {"name": "../app/legal-risk/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10758, "timestamp": 53763496912, "id": 121, "parentId": 108, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10746, "timestamp": 53763496927, "id": 122, "parentId": 108, "tags": {"name": "../app/project-definition/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10740, "timestamp": 53763496934, "id": 123, "parentId": 108, "tags": {"name": "../app/technical-layer/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10736, "timestamp": 53763496942, "id": 124, "parentId": 108, "tags": {"name": "../app/settings/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10732, "timestamp": 53763496948, "id": 125, "parentId": 108, "tags": {"name": "../app/vibe/page.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10728, "timestamp": 53763496955, "id": 126, "parentId": 108, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10723, "timestamp": 53763496962, "id": 127, "parentId": 108, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10719, "timestamp": 53763496969, "id": 128, "parentId": 108, "tags": {"name": "948.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10714, "timestamp": 53763496976, "id": 129, "parentId": 108, "tags": {"name": "471.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10710, "timestamp": 53763496983, "id": 130, "parentId": 108, "tags": {"name": "634.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10706, "timestamp": 53763496989, "id": 131, "parentId": 108, "tags": {"name": "972.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10702, "timestamp": 53763496996, "id": 132, "parentId": 108, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 10698, "timestamp": 53763497003, "id": 133, "parentId": 108, "tags": {"name": "42.js", "cache": "HIT"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "minify-js", "duration": 40345, "timestamp": 53763497013, "id": 134, "parentId": 108, "tags": {"name": "762.js", "cache": "MISS"}, "startTime": 1751774604212, "traceId": "d9a17e1b15669dd7"}, {"name": "terser-webpack-plugin-optimize", "duration": 57607, "timestamp": 53763479771, "id": 108, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1751774604194, "traceId": "d9a17e1b15669dd7"}, {"name": "css-minimizer-plugin", "duration": 235, "timestamp": 53763537566, "id": 135, "parentId": 19, "tags": {}, "startTime": 1751774604252, "traceId": "d9a17e1b15669dd7"}, {"name": "create-trace-assets", "duration": 2252, "timestamp": 53763538109, "id": 136, "parentId": 20, "tags": {}, "startTime": 1751774604253, "traceId": "d9a17e1b15669dd7"}, {"name": "seal", "duration": 407929, "timestamp": 53763148058, "id": 95, "parentId": 19, "tags": {}, "startTime": 1751774603863, "traceId": "d9a17e1b15669dd7"}, {"name": "webpack-compilation", "duration": 1906705, "timestamp": 53761672748, "id": 19, "parentId": 16, "tags": {"name": "server"}, "startTime": 1751774602387, "traceId": "d9a17e1b15669dd7"}, {"name": "emit", "duration": 200925, "timestamp": 53763580511, "id": 137, "parentId": 16, "tags": {}, "startTime": 1751774604295, "traceId": "d9a17e1b15669dd7"}, {"name": "webpack-close", "duration": 144750, "timestamp": 53763783278, "id": 138, "parentId": 16, "tags": {"name": "server"}, "startTime": 1751774604498, "traceId": "d9a17e1b15669dd7"}, {"name": "webpack-generate-error-stats", "duration": 11743, "timestamp": 53763928210, "id": 139, "parentId": 138, "tags": {}, "startTime": 1751774604643, "traceId": "d9a17e1b15669dd7"}, {"name": "run-webpack-compiler", "duration": 3986617, "timestamp": 53759954421, "id": 16, "parentId": 15, "tags": {}, "startTime": 1751774600669, "traceId": "d9a17e1b15669dd7"}, {"name": "format-webpack-messages", "duration": 235, "timestamp": 53763941063, "id": 140, "parentId": 15, "tags": {}, "startTime": 1751774604656, "traceId": "d9a17e1b15669dd7"}, {"name": "worker-main-server", "duration": 3988313, "timestamp": 53759953404, "id": 15, "parentId": 1, "tags": {}, "startTime": 1751774600668, "traceId": "d9a17e1b15669dd7"}, {"name": "create-entrypoints", "duration": 139166, "timestamp": 53767533397, "id": 143, "parentId": 141, "tags": {}, "startTime": 1751774608249, "traceId": "d9a17e1b15669dd7"}, {"name": "generate-webpack-config", "duration": 1394362, "timestamp": 53767673046, "id": 144, "parentId": 142, "tags": {}, "startTime": 1751774608388, "traceId": "d9a17e1b15669dd7"}, {"name": "make", "duration": 2763, "timestamp": 53769329722, "id": 146, "parentId": 145, "tags": {}, "startTime": 1751774610045, "traceId": "d9a17e1b15669dd7"}, {"name": "chunk-graph", "duration": 2790, "timestamp": 53769351144, "id": 148, "parentId": 147, "tags": {}, "startTime": 1751774610066, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-modules", "duration": 133, "timestamp": 53769354313, "id": 150, "parentId": 147, "tags": {}, "startTime": 1751774610070, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-chunks", "duration": 4968, "timestamp": 53769354838, "id": 151, "parentId": 147, "tags": {}, "startTime": 1751774610070, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-tree", "duration": 566, "timestamp": 53769360292, "id": 152, "parentId": 147, "tags": {}, "startTime": 1751774610076, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize-chunk-modules", "duration": 2454, "timestamp": 53769361709, "id": 153, "parentId": 147, "tags": {}, "startTime": 1751774610077, "traceId": "d9a17e1b15669dd7"}, {"name": "optimize", "duration": 10417, "timestamp": 53769354148, "id": 149, "parentId": 147, "tags": {}, "startTime": 1751774610069, "traceId": "d9a17e1b15669dd7"}, {"name": "module-hash", "duration": 272, "timestamp": 53769369975, "id": 154, "parentId": 147, "tags": {}, "startTime": 1751774610085, "traceId": "d9a17e1b15669dd7"}, {"name": "code-generation", "duration": 670, "timestamp": 53769370384, "id": 155, "parentId": 147, "tags": {}, "startTime": 1751774610086, "traceId": "d9a17e1b15669dd7"}, {"name": "hash", "duration": 1274, "timestamp": 53769371896, "id": 156, "parentId": 147, "tags": {}, "startTime": 1751774610087, "traceId": "d9a17e1b15669dd7"}, {"name": "code-generation-jobs", "duration": 384, "timestamp": 53769373159, "id": 157, "parentId": 147, "tags": {}, "startTime": 1751774610088, "traceId": "d9a17e1b15669dd7"}]