"use strict";(()=>{var e={};e.id=575,e.ids=[575],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},5100:(e,t,i)=>{i.r(t),i.d(t,{originalPathname:()=>f,patchFetch:()=>x,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>P,staticGenerationAsyncStorage:()=>h});var o={};i.r(o),i.d(o,{GET:()=>c,POST:()=>d,PUT:()=>l});var n=i(9303),r=i(8716),a=i(670),s=i(7070),p=i(2921);async function c(e){try{let{searchParams:t}=new URL(e.url),i=t.get("providerId");if(i){let e=(0,p.Mh)(i);if(!e)return s.NextResponse.json({error:"Provider not found"},{status:404});return s.NextResponse.json({provider:{id:e.id,name:e.name,icon:e.icon,description:e.description},models:e.models})}{let e=p.G4.map(e=>({id:e.id,name:e.name,icon:e.icon,description:e.description,isActive:e.isActive,modelCount:e.models.length,models:e.models}));return s.NextResponse.json({providers:e,totalProviders:p.G4.length,totalModels:p.G4.reduce((e,t)=>e+t.models.length,0)})}}catch(e){return console.error("Models API error:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let{providerId:t,apiKey:i,baseUrl:o}=await e.json();if(!t||!i)return s.NextResponse.json({error:"Provider ID and API key are required"},{status:400});let n=(0,p.Mh)(t);if(!n)return s.NextResponse.json({error:"Provider not found"},{status:404});let r=await u(t,i,o||n.baseUrl);if(r.success)return s.NextResponse.json({success:!0,provider:{id:n.id,name:n.name,icon:n.icon},models:r.models,defaultModels:n.models});return s.NextResponse.json({success:!1,error:r.error,defaultModels:n.models})}catch(e){return console.error("Models fetch error:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}async function u(e,t,i){try{let o;let n=[];switch(e){case"openai":case"openrouter":case"deepseek":case"groq":if((o=await fetch(`${i}/models`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).ok){let e=await o.json();n=e.data?.map(e=>({id:e.id,name:e.id,description:e.description||`${e.id} model`,contextLength:e.context_length||4096,created:e.created,owned_by:e.owned_by}))||[]}break;case"google":if((o=await fetch(`${i}/models?key=${t}`)).ok){let e=await o.json();n=e.models?.map(e=>({id:e.name.replace("models/",""),name:e.displayName||e.name,description:e.description||`${e.name} model`,contextLength:e.inputTokenLimit||32768,supportedGenerationMethods:e.supportedGenerationMethods}))||[]}break;case"anthropic":let r=(0,p.Mh)("anthropic");n=r?.models||[];break;default:if((o=await fetch(`${i}/models`,{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}})).ok){let e=await o.json();n=e.data?.map(e=>({id:e.id,name:e.id,description:e.description||`${e.id} model`,contextLength:e.context_length||4096}))||[]}}if(n.length>0)return{success:!0,models:n};return{success:!1,error:"No models found or API call failed"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function l(e){try{let{query:t,providerId:i}=await e.json();if(!t)return s.NextResponse.json({error:"Search query is required"},{status:400});let o=t.toLowerCase(),n=[];if(i){let e=(0,p.Mh)(i);e&&(n=e.models.filter(e=>e.name.toLowerCase().includes(o)||e.description.toLowerCase().includes(o)||e.id.toLowerCase().includes(o)).map(t=>({...t,provider:{id:e.id,name:e.name,icon:e.icon}})))}else for(let e of p.G4){let t=e.models.filter(e=>e.name.toLowerCase().includes(o)||e.description.toLowerCase().includes(o)||e.id.toLowerCase().includes(o)).map(t=>({...t,provider:{id:e.id,name:e.name,icon:e.icon}}));n.push(...t)}return s.NextResponse.json({query:t,results:n,totalResults:n.length})}catch(e){return console.error("Search error:",e),s.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/llm/models/route",pathname:"/api/llm/models",filename:"route",bundlePath:"app/api/llm/models/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ContextKit\\src\\app\\api\\llm\\models\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:P}=m,f="/api/llm/models/route";function x(){return(0,a.patchFetch)({serverHooks:P,staticGenerationAsyncStorage:h})}},2921:(e,t,i)=>{i.d(t,{G4:()=>o,Mh:()=>n,xP:()=>r});let o=[{id:"openai",name:"OpenAI",icon:"\uD83E\uDD16",description:"GPT models from OpenAI - Industry leading language models",baseUrl:"https://api.openai.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:128e3,models:[{id:"gpt-4o",name:"GPT-4o",description:"Most advanced multimodal model",contextLength:128e3,pricing:"$5/1M input, $15/1M output",inputPrice:5,outputPrice:15},{id:"gpt-4o-mini",name:"GPT-4o Mini",description:"Faster and more affordable",contextLength:128e3,pricing:"$0.15/1M input, $0.6/1M output",inputPrice:.15,outputPrice:.6},{id:"gpt-4-turbo",name:"GPT-4 Turbo",description:"High performance model",contextLength:128e3,pricing:"$10/1M input, $30/1M output",inputPrice:10,outputPrice:30},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",description:"Fast and efficient",contextLength:16385,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"anthropic",name:"Anthropic",icon:"\uD83E\uDDE0",description:"Claude models from Anthropic - Advanced reasoning capabilities",baseUrl:"https://api.anthropic.com/v1",apiKeyPlaceholder:"sk-ant-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"anthropic-version":"2023-06-01"},models:[{id:"claude-3-5-sonnet-20241022",name:"Claude 3.5 Sonnet",description:"Most intelligent model",contextLength:2e5,pricing:"$3/1M input, $15/1M output",inputPrice:3,outputPrice:15},{id:"claude-3-5-haiku-20241022",name:"Claude 3.5 Haiku",description:"Fastest model",contextLength:2e5,pricing:"$0.25/1M input, $1.25/1M output",inputPrice:.25,outputPrice:1.25},{id:"claude-3-opus-20240229",name:"Claude 3 Opus",description:"Most powerful model",contextLength:2e5,pricing:"$15/1M input, $75/1M output",inputPrice:15,outputPrice:75}]},{id:"google",name:"Google AI",icon:"\uD83D\uDD0D",description:"Gemini models from Google - Multimodal AI capabilities",baseUrl:"https://generativelanguage.googleapis.com/v1beta",apiKeyPlaceholder:"AIza...",isActive:!0,supportsStreaming:!0,maxTokens:2e6,models:[{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",description:"Advanced reasoning with 2M context",contextLength:2e6,pricing:"$1.25/1M input, $5/1M output",inputPrice:1.25,outputPrice:5},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",description:"Fast and efficient",contextLength:1e6,pricing:"$0.075/1M input, $0.3/1M output",inputPrice:.075,outputPrice:.3},{id:"gemini-pro",name:"Gemini Pro",description:"Balanced performance",contextLength:32768,pricing:"$0.5/1M input, $1.5/1M output",inputPrice:.5,outputPrice:1.5}]},{id:"openrouter",name:"OpenRouter",icon:"\uD83D\uDD00",description:"Access to multiple models via OpenRouter - One API for all models",baseUrl:"https://openrouter.ai/api/v1",apiKeyPlaceholder:"sk-or-...",isActive:!0,supportsStreaming:!0,maxTokens:2e5,headers:{"HTTP-Referer":process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000","X-Title":"ContextKit"},models:[{id:"openai/gpt-4o",name:"GPT-4o (via OpenRouter)",description:"OpenAI GPT-4o through OpenRouter",contextLength:128e3,pricing:"Variable pricing"},{id:"anthropic/claude-3.5-sonnet",name:"Claude 3.5 Sonnet (via OpenRouter)",description:"Anthropic Claude through OpenRouter",contextLength:2e5,pricing:"Variable pricing"},{id:"google/gemini-pro-1.5",name:"Gemini Pro 1.5 (via OpenRouter)",description:"Google Gemini through OpenRouter",contextLength:1e6,pricing:"Variable pricing"},{id:"meta-llama/llama-3.1-405b-instruct",name:"Llama 3.1 405B (via OpenRouter)",description:"Meta Llama through OpenRouter",contextLength:131072,pricing:"Variable pricing"}]},{id:"deepseek",name:"DeepSeek",icon:"\uD83C\uDF0A",description:"DeepSeek models - Efficient and cost-effective AI",baseUrl:"https://api.deepseek.com/v1",apiKeyPlaceholder:"sk-...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"deepseek-chat",name:"DeepSeek Chat",description:"General purpose conversational AI",contextLength:32768,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28},{id:"deepseek-coder",name:"DeepSeek Coder",description:"Specialized for code generation",contextLength:16384,pricing:"$0.14/1M input, $0.28/1M output",inputPrice:.14,outputPrice:.28}]},{id:"groq",name:"Groq",icon:"⚡",description:"Groq - Ultra-fast inference with GroqChip technology",baseUrl:"https://api.groq.com/openai/v1",apiKeyPlaceholder:"gsk_...",isActive:!0,supportsStreaming:!0,maxTokens:32768,models:[{id:"llama-3.1-70b-versatile",name:"Llama 3.1 70B",description:"Meta Llama 3.1 70B on Groq",contextLength:131072,pricing:"$0.59/1M input, $0.79/1M output",inputPrice:.59,outputPrice:.79},{id:"llama-3.1-8b-instant",name:"Llama 3.1 8B",description:"Meta Llama 3.1 8B on Groq",contextLength:131072,pricing:"$0.05/1M input, $0.08/1M output",inputPrice:.05,outputPrice:.08},{id:"mixtral-8x7b-32768",name:"Mixtral 8x7B",description:"Mistral Mixtral 8x7B on Groq",contextLength:32768,pricing:"$0.24/1M input, $0.24/1M output",inputPrice:.24,outputPrice:.24}]}];function n(e){return o.find(t=>t.id===e)}function r(e){let t=n(e);return t?.headers||{}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),o=t.X(0,[948,972],()=>i(5100));module.exports=o})();